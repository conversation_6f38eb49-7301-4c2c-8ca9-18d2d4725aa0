@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  /* Updated glass colors */
  --glass-light: rgba(255, 255, 255, 0.08);
  --glass-dark: rgba(17, 24, 39, 0.15);
  --glass-darker: rgba(0, 0, 0, 0.2);
  
  /* Updated primary colors */
  --primary-25: #f5f8ff;
  --primary-50: #eef4ff;
  --primary-100: #e0e9ff;
  --primary-200: #c7d8fe;
  --primary-300: #a5bffd;
  --primary-400: #7fa0fa;
  --primary-500: #6178f5;
  --primary-600: #4d5beb;
  --primary-700: #3e46d6;
  --primary-800: #333aad;
  --primary-900: #2f3589;
  --primary-950: #1e2252;
  
  /* Status colors */
  --success-500: #22c55e;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #0ea5e9;
}

/* Base Styles */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  /* Better scrollbar handling with fallbacks */
  -webkit-scrollbar-gutter: stable; /* WebKit first */
  scrollbar-gutter: stable; /* Standard property */
  font-size: 16px;
  line-height: 1.5;
  
  /* Fallback for browsers that don't support scrollbar-gutter */
  padding-right: 0.5rem;
  overflow-y: scroll;
}

/* Typography */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%);
  color: #f8fafc;
  min-height: 100vh;
  scrollbar-gutter: stable;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.25;
  margin-bottom: 0.75em;
  letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Paragraphs */
p {
  margin-bottom: 1.25em;
  line-height: 1.7;
  color: #e2e8f0;
}

/* Links */
a {
  color: var(--primary-400);
  text-decoration: none;
  transition: color 0.2s ease, opacity 0.2s ease;
}

a:hover {
  color: var(--primary-300);
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Buttons */
button, .btn {
  transition: all 0.2s ease;
  transform-origin: center;
}

button:active, .btn:active {
  transform: scale(0.98);
}

/* Form Elements */
input, textarea, select {
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  color: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-scale:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.5);
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Utility Classes */
.text-balance {
  /* Fallback for older browsers */
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  
  /* Modern browsers */
  text-wrap: balance;
}

.text-gradient {
  -webkit-background-clip: text; /* Safari/Chrome */
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* Fallback */
}

/* Custom Scrollbar - WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Standard scrollbar for Firefox */
@supports (scrollbar-width: thin) {
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) rgba(255, 255, 255, 0.05);
  }
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Glassmorphism Utilities */
.glass-light {
  background: var(--glass-light);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: var(--glass-dark);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Glasmorphism Components */
.glass-card {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg;
}

.glass-button {
  @apply bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/30 transition-all duration-300;
}

.glass-input {
  @apply bg-white/5 backdrop-blur-sm border border-white/20 focus:border-primary-400 rounded-lg;
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-transparent;
}

/* Button Hover Effects */
.btn-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-105 active:scale-95;
}

/* Card Hover Effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-glow hover:scale-[1.02];
}

/* Status Indicators */
.status-online {
  @apply w-2 h-2 bg-green-500 rounded-full animate-pulse;
}

.status-offline {
  @apply w-2 h-2 bg-red-500 rounded-full;
}

.status-busy {
  @apply w-2 h-2 bg-yellow-500 rounded-full animate-pulse;
}

/* Loading Spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
}

/* Custom Input Styles */
.input-glass {
  @apply bg-glass-dark backdrop-blur-sm border border-white/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

/* Custom Select Styles */
.select-glass {
  @apply bg-glass-dark backdrop-blur-sm border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

/* Notification Styles */
.notification {
  @apply p-4 rounded-lg border backdrop-blur-sm;
}

.notification-info {
  @apply bg-blue-500/20 border-blue-500/30 text-blue-300;
}

.notification-success {
  @apply bg-green-500/20 border-green-500/30 text-green-300;
}

.notification-warning {
  @apply bg-yellow-500/20 border-yellow-500/30 text-yellow-300;
}

.notification-error {
  @apply bg-red-500/20 border-red-500/30 text-red-300;
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 641px) {
  .desktop-hidden {
    display: none;
  }
}

/* Mobile Optimizations */
@media (max-width: 1024px) {
  .touch-manipulation {
    touch-action: manipulation;
  }

  .sidebar-mobile {
    overscroll-behavior: contain;
  }
}

/* Animation Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Touch Feedback */
.touch-feedback {
  -webkit-tap-highlight-color: transparent;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* Hardware Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
