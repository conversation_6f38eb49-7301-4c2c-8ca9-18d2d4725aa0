@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  /* Updated glass colors */
  --glass-light: rgba(255, 255, 255, 0.08);
  --glass-dark: rgba(17, 24, 39, 0.15);
  --glass-darker: rgba(0, 0, 0, 0.2);
  
  /* Updated primary colors */
  --primary-25: #f5f8ff;
  --primary-50: #eef4ff;
  --primary-100: #e0e9ff;
  --primary-200: #c7d8fe;
  --primary-300: #a5bffd;
  --primary-400: #7fa0fa;
  --primary-500: #6178f5;
  --primary-600: #4d5beb;
  --primary-700: #3e46d6;
  --primary-800: #333aad;
  --primary-900: #2f3589;
  --primary-950: #1e2252;
  
  /* Status colors */
  --success-500: #22c55e;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #0ea5e9;
}

/* Base Styles */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling with enhanced compatibility */
html {
  /* Smooth scrolling for browsers that support it */
  @media (prefers-reduced-motion: no-preference) {
    & {
      scroll-behavior: smooth;
    }
  }
  
  /* Scrollbar gutter handling with progressive enhancement */
  overflow-y: scroll; /* Always show scrollbar to prevent layout shift */
  padding-right: 0.5rem; /* Fallback padding */
  
  @supports (scrollbar-gutter: stable) or (-webkit-scrollbar-gutter: stable) {
    & {
      -webkit-scrollbar-gutter: stable; /* WebKit first */
      scrollbar-gutter: stable; /* Standard property */
      padding-right: 0; /* Reset fallback padding when supported */
    }
  }
  
  /* Base typography */
  font-size: 16px;
  line-height: 1.5;
  
  /* Enhanced text wrapping with fallbacks */
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  
  /* Modern text wrapping with feature detection */
  @supports (text-wrap: pretty) {
    & {
      text-wrap: pretty;
    }
  }
}

/* Typography */
body {
  font-family: theme('fontFamily.sans');
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%);
  color: theme('colors.gray.100');
  min-height: 100vh;
  scrollbar-gutter: stable;
  line-height: 1.6;
  letter-spacing: 0.01em;
  font-size: 1rem;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Fluid Typography */
@media (min-width: 640px) {
  html {
    font-size: 16px;
  }
}

/* Typographic Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.5em;
  color: theme('colors.white');
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.5rem; /* 40px */
  line-height: 1.1;
  margin-bottom: 1rem;
  font-weight: 800;
  letter-spacing: -0.05em;
}

h2 {
  font-size: 2rem; /* 32px */
  line-height: 1.15;
  margin-bottom: 0.75rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.25;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.3;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

h5 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.35;
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

h6 {
  font-size: 1rem; /* 16px */
  line-height: 1.4;
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  color: theme('colors.gray.300');
}

/* Paragraphs */
p {
  margin-bottom: 1.25em;
  line-height: 1.7;
  color: theme('colors.gray.200');
  max-width: 65ch;
}

/* Text Styles */
.lead {
  font-size: 1.25rem;
  line-height: 1.6;
  color: theme('colors.gray.200');
  margin-bottom: 1.5rem;
}

.text-small {
  font-size: 0.875rem;
  line-height: 1.5;
}

.text-xsmall {
  font-size: 0.75rem;
  line-height: 1.4;
  letter-spacing: 0.01em;
}

/* Text Colors */
.text-muted {
  color: theme('colors.gray.400');
}

.text-emphasis {
  color: theme('colors.white');
  font-weight: 500;
}

/* Links */
a:not(.btn) {
  color: theme('colors.primary.400');
  text-decoration: none;
  transition: color 0.2s ease, opacity 0.2s ease;
  border-bottom: 1px solid transparent;
  padding-bottom: 0.1em;
}

a:not(.btn):hover {
  color: theme('colors.primary.300');
  border-bottom-color: currentColor;
}

/* Lists */
ul, ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: 0.5em;
  line-height: 1.6;
}

/* Blockquotes */
blockquote {
  border-left: 4px solid theme('colors.primary.500');
  padding-left: 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: theme('colors.gray.300');
}

/* Code */
code {
  font-family: theme('fontFamily.mono');
  background: theme('colors.gray.800');
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.9em;
  color: theme('colors.primary.300');
}

pre {
  background: theme('colors.gray.900');
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  font-size: 0.9em;
  line-height: 1.5;
}

pre code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  color: inherit;
  font-size: 1em;
}

/* Horizontal Rule */
hr {
  border: 0;
  height: 1px;
  background: theme('colors.gray.700');
  margin: 2rem 0;
}

/* Text Selection */
::selection {
  background: theme('colors.primary.500');
  color: white;
  text-shadow: none;
}

/* Links */
a {
  color: var(--primary-400);
  text-decoration: none;
  transition: color 0.2s ease, opacity 0.2s ease;
}

a:hover {
  color: var(--primary-300);
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Buttons */
button, .btn {
  transition: all 0.2s ease;
  transform-origin: center;
}

button:active, .btn:active {
  transform: scale(0.98);
}

/* Form Elements */
input, textarea, select {
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  color: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-scale:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.5);
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Utility Classes */
.text-balance {
  /* Fallback for older browsers */
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  
  /* Modern browsers */
  text-wrap: balance;
}

.text-gradient {
  -webkit-background-clip: text; /* Safari/Chrome */
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* Fallback */
}

/* Custom Scrollbar - WebKit browsers */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #4d5beb; /* primary-600 */
  border-radius: 5px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #6178f5; /* primary-500 */
}

/* Standard scrollbar for modern browsers */
@supports (scrollbar-color: auto) {
  html {
    scrollbar-width: thin;
    scrollbar-color: #4d5beb rgba(0, 0, 0, 0.2);
  }
}

/* Fallback for older browsers */
@supports not (scrollbar-color: auto) {
  /* Additional fallback styles if needed */
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Glassmorphism Utilities */
.glass-light {
  background: var(--glass-light);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: var(--glass-dark);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Glasmorphism Components */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.glass-button {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.glass-input {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.glass-input:focus {
  border-color: #6178f5;
  outline: none;
  box-shadow: 0 0 0 2px rgba(97, 120, 245, 0.5);
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Focus Styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
}

/* Button Hover Effects */
.btn-hover {
  transition: all 200ms;
}

.btn-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.btn-hover:active {
  transform: scale(0.95);
}

/* Card Hover Effects */
.card-hover {
  transition: all 300ms;
}

.card-hover:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: scale(1.02);
}

/* Status Indicators */
.status-online {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #10b981;
  border-radius: 9999px;
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-offline {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #6b7280;
  border-radius: 9999px;
}

.status-away {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #f59e0b;
  border-radius: 9999px;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-busy {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #ef4444;
  border-radius: 9999px;
}

.status-invisible {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #9ca3af;
  border-radius: 9999px;
  opacity: 0.7;
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border: 2px solid #d1d5db;
  border-top: 2px solid #6366f1;
  border-radius: 9999px;
  width: 1rem;
  height: 1rem;
}

/* Custom Input Styles */
.input-glass {
  background: var(--glass-dark);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  color: white;
  transition: all 0.2s;
}

.input-glass::placeholder {
  color: #9ca3af;
}

.input-glass:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(97, 120, 245, 0.5);
  border-color: transparent;
}

/* Custom Select Styles */
.select-glass {
  background: var(--glass-dark);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  color: white;
  transition: all 0.2s;
}

.select-glass:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(97, 120, 245, 0.5);
  border-color: transparent;
}

/* Notification Styles */
.notification {
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.notification-info {
  background: rgba(14, 165, 233, 0.2);
  border-color: rgba(14, 165, 233, 0.3);
  color: rgb(147, 197, 253);
}

.notification-success {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
  color: rgb(134, 239, 172);
}

.notification-warning {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.3);
  color: rgb(253, 230, 138);
}

.notification-error {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: rgb(252, 165, 165);
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 641px) {
  .desktop-hidden {
    display: none;
  }
}

/* Mobile Optimizations */
@media (max-width: 1024px) {
  .touch-manipulation {
    touch-action: manipulation;
  }

  .sidebar-mobile {
    overscroll-behavior: contain;
  }
}

/* Animation Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Touch Feedback */
.touch-feedback {
  -webkit-tap-highlight-color: transparent;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* Hardware Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Spacing Utilities */
.space-y-0 > * + * { margin-top: 0; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-5 > * + * { margin-top: 1.25rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-7 > * + * { margin-top: 1.75rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-y-9 > * + * { margin-top: 2.25rem; }
.space-y-10 > * + * { margin-top: 2.5rem; }
.space-y-11 > * + * { margin-top: 2.75rem; }
.space-y-12 > * + * { margin-top: 3rem; }
.space-y-14 > * + * { margin-top: 3.5rem; }
.space-y-16 > * + * { margin-top: 4rem; }
.space-y-20 > * + * { margin-top: 5rem; }
.space-y-24 > * + * { margin-top: 6rem; }
.space-y-28 > * + * { margin-top: 7rem; }
.space-y-32 > * + * { margin-top: 8rem; }
.space-y-36 > * + * { margin-top: 9rem; }
.space-y-40 > * + * { margin-top: 10rem; }
.space-y-44 > * + * { margin-top: 11rem; }
.space-y-48 > * + * { margin-top: 12rem; }
.space-y-52 > * + * { margin-top: 13rem; }
.space-y-56 > * + * { margin-top: 14rem; }
.space-y-60 > * + * { margin-top: 15rem; }
.space-y-64 > * + * { margin-top: 16rem; }
.space-y-72 > * + * { margin-top: 18rem; }
.space-y-80 > * + * { margin-top: 20rem; }
.space-y-96 > * + * { margin-top: 24rem; }

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
