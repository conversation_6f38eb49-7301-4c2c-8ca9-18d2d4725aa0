
import { Layout } from '@/components/layout/Layout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { ToastProvider } from '@/contexts/ToastContext';
import { AIQuoteWizard } from '@/pages/AIQuoteWizard';
import { Analytics } from '@/pages/Analytics';
import { Customers } from '@/pages/Customers';
import { Dashboard } from '@/pages/Dashboard';
import { Email } from '@/pages/Email';
import { InnovarsAI } from '@/pages/InnovarsAI';
import { Invoices } from '@/pages/Invoices';
import { Projects } from '@/pages/Projects';
import { Quotes } from '@/pages/Quotes';
import { Settings } from '@/pages/Settings';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import AISmartQuote from './pages/AISmartQuote';
import './styles/globals.css';

function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/quotes" element={<Quotes />} />
              <Route path="/quotes/new-ai" element={<AIQuoteWizard />} />
              <Route path="/customers" element={<Customers />} />
              <Route path="/projects" element={<Projects />} />
              <Route path="/innovars-ai" element={<InnovarsAI />} />
              {/* Backward compatibility route */}
              <Route path="/rita" element={<InnovarsAI />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/email" element={<Email />} />
              <Route path="/invoices" element={<Invoices />} />
              <Route path="/ai-smart-quote" element={<AISmartQuote />} />
              {/* Add more routes here as we build them */}
            </Routes>
          </Layout>
        </Router>
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;
