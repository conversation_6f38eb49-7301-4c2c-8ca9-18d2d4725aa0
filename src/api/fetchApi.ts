import { fetchWithToast, get, post, put, del, patch, FetchOptions } from '@/lib/fetchWithToast'
import { API_BASE_URL } from '@/constants'

/**
 * Enhanced API client met fetchWithToast voor betere error handling
 * Alternatief voor axios-based client met automatische toast notifications
 */
export class FetchApiClient {
  private baseURL: string
  private defaultOptions: FetchOptions

  constructor(baseURL: string = API_BASE_URL, options: FetchOptions = {}) {
    this.baseURL = baseURL
    this.defaultOptions = {
      showToast: true,
      ...options,
    }
  }

  /**
   * Voeg auth token toe aan requests
   */
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('auth_token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  /**
   * Maak volledige URL
   */
  private makeUrl(endpoint: string): string {
    return `${this.baseURL}${endpoint}`
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, options: FetchOptions = {}): Promise<T> {
    const response = await get<T>(this.makeUrl(endpoint), {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })
    return response.data
  }

  /**
   * POST request
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    const response = await post<T>(this.makeUrl(endpoint), data, {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })
    return response.data
  }

  /**
   * PUT request
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    const response = await put<T>(this.makeUrl(endpoint), data, {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })
    return response.data
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, options: FetchOptions = {}): Promise<T> {
    const response = await del<T>(this.makeUrl(endpoint), {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })
    return response.data
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    endpoint: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    const response = await patch<T>(this.makeUrl(endpoint), data, {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })
    return response.data
  }

  /**
   * Upload file(s)
   */
  async upload<T = any>(
    endpoint: string,
    files: File | File[],
    options: FetchOptions = {}
  ): Promise<T> {
    const formData = new FormData()
    
    if (Array.isArray(files)) {
      files.forEach((file, index) => {
        formData.append(`file${index}`, file)
      })
    } else {
      formData.append('file', files)
    }

    const response = await fetchWithToast<T>(this.makeUrl(endpoint), {
      ...this.defaultOptions,
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        ...this.getAuthHeaders(),
        // Don't set Content-Type for FormData
        ...Object.fromEntries(
          Object.entries(options.headers || {}).filter(
            ([key]) => key.toLowerCase() !== 'content-type'
          )
        ),
      },
    })
    return response.data
  }
}

// Default API client instance
export const fetchApi = new FetchApiClient()

// Export voor backward compatibility
export default fetchApi 