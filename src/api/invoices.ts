import { fetchApi } from './fetchApi';
import { 
  Invoice, 
  CreateInvoiceRequest, 
  CreateInvoiceResponse,
  AIInvoiceRequest,
  AIInvoiceResponse 
} from '@/types/invoice';

// Mock data voor development
const mockInvoices: Invoice[] = [
  {
    id: '1',
    number: 'INV-2024-001',
    customerId: '1',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerAddress: 'Hoofdstraat 123, 1234 AB Amsterdam',
    projectName: 'Renovatie Badkamer',
    invoiceDate: '2024-01-15',
    dueDate: '2024-02-14',
    vatPercentage: 21,
    subtotal: 2500,
    vatAmount: 525,
    total: 3025,
    status: 'sent',
    items: [
      {
        id: '1',
        description: '<PERSON><PERSON><PERSON> kozi<PERSON><PERSON> met dubbele beglazing',
        quantity: 4,
        unitPrice: 450,
        total: 1800,
        vatRate: 21,
        vatAmount: 378
      },
      {
        id: '2',
        description: 'Installatie en montage',
        quantity: 1,
        unitPrice: 700,
        total: 700,
        vatRate: 21,
        vatAmount: 147
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    number: 'INV-2024-002',
    customerId: '2',
    customerName: 'Marie Dubois',
    customerEmail: '<EMAIL>',
    customerAddress: 'Rue de la Paix 45, 75001 Paris',
    projectName: 'Keuken Renovatie',
    invoiceDate: '2024-01-20',
    dueDate: '2024-02-19',
    vatPercentage: 21,
    subtotal: 1800,
    vatAmount: 378,
    total: 2178,
    status: 'paid',
    items: [
      {
        id: '3',
        description: 'Keukenkasten en werkblad',
        quantity: 1,
        unitPrice: 1200,
        total: 1200,
        vatRate: 21,
        vatAmount: 252
      },
      {
        id: '4',
        description: 'Keukenapparatuur',
        quantity: 1,
        unitPrice: 600,
        total: 600,
        vatRate: 21,
        vatAmount: 126
      }
    ],
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  }
];

// Mock quotes voor dropdown
const mockQuotes = [
  {
    id: '1',
    number: 'QT-2024-001',
    customerName: 'Jan Vermeulen',
    projectName: 'Renovatie Badkamer',
    status: 'approved',
    total: 2500
  },
  {
    id: '2', 
    number: 'QT-2024-002',
    customerName: 'Marie Dubois',
    projectName: 'Keuken Renovatie',
    status: 'approved',
    total: 1800
  },
  {
    id: '3',
    number: 'QT-2024-003', 
    customerName: 'Piet Bakker',
    projectName: 'Tuin Aanleg',
    status: 'pending',
    total: 3200
  }
];

export const invoiceApi = {
  // Haal alle facturen op
  async getInvoices(): Promise<Invoice[]> {
    try {
      // In production zou dit een echte API call zijn
      // const response = await fetchApi.get('/invoices');
      // return response.data;
      
      // Mock data voor development
      return new Promise((resolve) => {
        setTimeout(() => resolve(mockInvoices), 500);
      });
    } catch (error) {
      console.error('Error loading invoices:', error);
      throw new Error('Fout bij het laden van facturen');
    }
  },

  // Haal factuur op basis van ID
  async getInvoice(id: string): Promise<Invoice> {
    try {
      const invoice = mockInvoices.find(inv => inv.id === id);
      if (!invoice) {
        throw new Error('Factuur niet gevonden');
      }
      return invoice;
    } catch (error) {
      console.error('Error loading invoice:', error);
      throw new Error('Fout bij het laden van factuur');
    }
  },

  // Haal goedgekeurde quotes op voor dropdown
  async getApprovedQuotes() {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          const approvedQuotes = mockQuotes.filter(quote => quote.status === 'approved');
          resolve(approvedQuotes);
        }, 300);
      });
    } catch (error) {
      console.error('Error loading approved quotes:', error);
      throw new Error('Fout bij het laden van goedgekeurde offertes');
    }
  },

  // Maak nieuwe factuur (handmatig)
  async createInvoice(request: CreateInvoiceRequest): Promise<CreateInvoiceResponse> {
    try {
      // Simuleer API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const invoiceNumber = `INV-${new Date().getFullYear()}-${String(mockInvoices.length + 1).padStart(3, '0')}`;
      const invoiceDate = request.invoiceDate || new Date().toISOString().split('T')[0];
      const dueDate = request.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const vatPercentage = request.vatPercentage || 21;

      const items = request.manualItems?.map((item, index) => ({
        id: String(index + 1),
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
        vatRate: vatPercentage,
        vatAmount: (item.quantity * item.unitPrice) * (vatPercentage / 100)
      })) || [];

      const subtotal = items.reduce((sum, item) => sum + item.total, 0);
      const vatAmount = items.reduce((sum, item) => sum + item.vatAmount, 0);
      const total = subtotal + vatAmount;

      const newInvoice: Invoice = {
        id: String(mockInvoices.length + 1),
        number: invoiceNumber,
        customerId: request.customerId,
        customerName: 'Klant Naam', // Zou uit customer service komen
        projectName: request.projectName,
        invoiceDate,
        dueDate,
        vatPercentage,
        subtotal,
        vatAmount,
        total,
        status: 'draft',
        items,
        pdfUrl: `/uploads/invoices/${invoiceNumber}.pdf`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      mockInvoices.push(newInvoice);

      return {
        success: true,
        data: {
          invoiceId: newInvoice.id,
          pdfUrl: newInvoice.pdfUrl!,
          invoice: newInvoice
        }
      };
    } catch (error) {
      console.error('Error creating invoice:', error);
      return {
        success: false,
        message: 'Fout bij het aanmaken van factuur'
      };
    }
  },

  // AI Factuur Wizard
  async createAIInvoice(request: AIInvoiceRequest): Promise<AIInvoiceResponse> {
    try {
      // Simuleer AI processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const invoiceNumber = `INV-${new Date().getFullYear()}-${String(mockInvoices.length + 1).padStart(3, '0')}`;
      const invoiceDate = request.invoiceDate || new Date().toISOString().split('T')[0];
      const dueDate = request.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const vatPercentage = request.vatPercentage || 21;

      let items: Invoice['items'] = [];
      let aiDescription = '';

      if (request.quoteId) {
        // Haal items uit quote (mock data)
        const quote = mockQuotes.find(q => q.id === request.quoteId);
        if (quote) {
          items = [
            {
              id: '1',
              description: `Factuur voor ${quote.projectName}`,
              quantity: 1,
              unitPrice: quote.total,
              total: quote.total,
              vatRate: vatPercentage,
              vatAmount: quote.total * (vatPercentage / 100)
            }
          ];
          aiDescription = `Factuur gegenereerd op basis van goedgekeurde offerte ${quote.number} voor project "${quote.projectName}".`;
        }
      } else if (request.manualItems) {
        // Gebruik handmatig ingevoerde items
        items = request.manualItems.map((item, index) => ({
          id: String(index + 1),
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.quantity * item.unitPrice,
          vatRate: vatPercentage,
          vatAmount: (item.quantity * item.unitPrice) * (vatPercentage / 100)
        }));
        aiDescription = 'Factuur gegenereerd op basis van handmatig ingevoerde items met AI-optimalisatie van beschrijvingen.';
      }

      const subtotal = items.reduce((sum, item) => sum + item.total, 0);
      const vatAmount = items.reduce((sum, item) => sum + item.vatAmount, 0);
      const total = subtotal + vatAmount;

      const newInvoice: Invoice = {
        id: String(mockInvoices.length + 1),
        number: invoiceNumber,
        customerId: request.customerId,
        customerName: 'Klant Naam', // Zou uit customer service komen
        projectName: request.projectName,
        invoiceDate,
        dueDate,
        vatPercentage,
        subtotal,
        vatAmount,
        total,
        status: 'draft',
        items,
        pdfUrl: `/uploads/invoices/${invoiceNumber}.pdf`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      mockInvoices.push(newInvoice);

      return {
        success: true,
        data: {
          invoiceId: newInvoice.id,
          pdfUrl: newInvoice.pdfUrl!,
          invoice: newInvoice,
          aiDescription
        }
      };
    } catch (error) {
      console.error('Error creating AI invoice:', error);
      return {
        success: false,
        message: 'Fout bij het genereren van AI factuur'
      };
    }
  },

  // Update factuur status
  async updateInvoiceStatus(id: string, status: Invoice['status']): Promise<boolean> {
    try {
      const invoice = mockInvoices.find(inv => inv.id === id);
      if (invoice) {
        invoice.status = status;
        invoice.updatedAt = new Date().toISOString();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating invoice status:', error);
      return false;
    }
  },

  // Verwijder factuur
  async deleteInvoice(id: string): Promise<boolean> {
    try {
      const index = mockInvoices.findIndex(inv => inv.id === id);
      if (index !== -1) {
        mockInvoices.splice(index, 1);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }
}; 