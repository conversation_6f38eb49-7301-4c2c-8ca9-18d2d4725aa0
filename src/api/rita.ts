import { api, withRetry } from './client';

// Rita AI API types
export interface VoiceMessage {
  audioBlob: Blob;
  duration: number;
  language: 'nl' | 'fr';
}

export interface AiResponse {
  text: string;
  confidence: number;
  suggestions: string[];
  intent: 'quote_creation' | 'quote_update' | 'customer_info' | 'general_query';
}

export interface QuoteSuggestion {
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    category: string;
  }>;
  totalAmount: number;
  reasoning: string;
}

export interface PriceOptimization {
  currentPrice: number;
  suggestedPrice: number;
  marketAverage: number;
  reasoning: string;
  confidence: number;
}

// Rita AI API services
export const ritaApi = {
  // Process voice message
  processVoiceMessage: async (voiceMessage: VoiceMessage) => {
    const formData = new FormData();
    formData.append('audio', voiceMessage.audioBlob);
    formData.append('duration', voiceMessage.duration.toString());
    formData.append('language', voiceMessage.language);
    
    return withRetry(() => api.post<AiResponse>('/rita/voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }));
  },

  // Process text message
  processTextMessage: async (message: string, language: 'nl' | 'fr' = 'nl') => {
    return withRetry(() => api.post<AiResponse>('/rita/text', {
      message,
      language,
    }));
  },

  // Generate quote suggestions
  generateQuoteSuggestions: async (projectDescription: string, customerInfo?: any) => {
    return withRetry(() => api.post<QuoteSuggestion>('/rita/suggestions', {
      projectDescription,
      customerInfo,
    }));
  },

  // Optimize pricing
  optimizePricing: async (quoteItems: any[], marketData?: any) => {
    return withRetry(() => api.post<PriceOptimization>('/rita/optimize-pricing', {
      quoteItems,
      marketData,
    }));
  },

  // Get AI status
  getAiStatus: async () => {
    return withRetry(() => api.get('/rita/status'));
  },

  // Get conversation history
  getConversationHistory: async (limit: number = 50) => {
    return withRetry(() => api.get(`/rita/conversations?limit=${limit}`));
  },

  // Save conversation
  saveConversation: async (conversation: {
    messages: Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date }>;
    summary: string;
  }) => {
    return withRetry(() => api.post('/rita/conversations', conversation));
  },

  // Get AI insights
  getAiInsights: async () => {
    return withRetry(() => api.get('/rita/insights'));
  },

  // Train AI with feedback
  provideFeedback: async (conversationId: string, feedback: {
    rating: number;
    comment?: string;
    corrections?: string[];
  }) => {
    return withRetry(() => api.post(`/rita/feedback/${conversationId}`, feedback));
  },

  // Get market analysis
  getMarketAnalysis: async (location: string, sector: string) => {
    return withRetry(() => api.get(`/rita/market-analysis?location=${encodeURIComponent(location)}&sector=${encodeURIComponent(sector)}`));
  },

  // Generate project templates
  generateProjectTemplates: async (projectType: string, location: string) => {
    return withRetry(() => api.get(`/rita/templates?projectType=${encodeURIComponent(projectType)}&location=${encodeURIComponent(location)}`));
  },

  // Validate quote completeness
  validateQuote: async (quoteData: any) => {
    return withRetry(() => api.post('/rita/validate-quote', quoteData));
  },

  // Get energy improvement suggestions
  getEnergySuggestions: async (projectDetails: any) => {
    return withRetry(() => api.post('/rita/energy-suggestions', projectDetails));
  },

  // Process WhatsApp integration
  processWhatsAppMessage: async (message: string, phoneNumber: string) => {
    return withRetry(() => api.post('/rita/whatsapp', {
      message,
      phoneNumber,
    }));
  },
}; 