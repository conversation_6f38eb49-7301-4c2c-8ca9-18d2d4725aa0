// API exports
export { api, apiClient, withRetry } from './client';
export type { ApiResponse, ApiError } from './client';

// Quote API
export { quoteApi } from './quotes';
export type { 
  CreateQuoteRequest, 
  UpdateQuoteRequest, 
  QuoteFilters 
} from './quotes';

// Customer API
export { customerApi } from './customers';
export type { 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerFilters 
} from './customers';

// Innovars AI API (new)
export { innovarsAIApi } from './innovars-ai';
export type { 
  VoiceMessage, 
  AIResponse, 
  ProcessTextMessageRequest,
  QuoteSuggestion, 
  PriceOptimization,
  MarketInsight
} from './innovars-ai';

// Backward compatibility for Rita API
export { innovarsAIApi as ritaApi } from './innovars-ai';
export type { 
  AIResponse as AiResponse
} from './innovars-ai';

// Invoice API
export { invoiceApi } from './invoices';
export type { 
  CreateInvoiceRequest, 
  CreateInvoiceResponse,
  AIInvoiceRequest,
  AIInvoiceResponse,
  Invoice,
  InvoiceItem
} from '@/types/invoice';