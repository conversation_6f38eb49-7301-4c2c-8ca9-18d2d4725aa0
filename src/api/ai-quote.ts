import { QuoteItem } from '@/stores/useAppStore';

export interface Dimension {
  id: string;
  length: number;
  width: number;
  height: number;
  quantity: number;
  description: string;
}

export interface AIQuoteRequest {
  title: string;
  dimensions: Dimension[];
  photos: string[]; // base64 encoded images
}

export interface AIQuoteResponse {
  title: string;
  items: QuoteItem[];
  subtotal: number;
  vat: number;
  total: number;
  description: string;
}

// Mock material detection based on photo analysis
const detectMaterials = (photos: string[]): string[] => {
  // In a real implementation, this would use Vision AI
  const materials = [
    'hout',
    'tegels',
    'aluminium',
    'glas',
    'staal',
    'kunststof',
    'beton',
    'marmer'
  ];
  
  // Randomly select materials based on photo count
  const detectedMaterials: string[] = [];
  for (let i = 0; i < Math.min(photos.length, 3); i++) {
    const randomMaterial = materials[Math.floor(Math.random() * materials.length)];
    if (!detectedMaterials.includes(randomMaterial)) {
      detectedMaterials.push(randomMaterial);
    }
  }
  
  return detectedMaterials.length > 0 ? detectedMaterials : ['hout', 'tegels'];
};

// Mock calculation service
const calculateCosts = (dimensions: Dimension[], materials: string[]): QuoteItem[] => {
  const items: QuoteItem[] = [];
  
  // Price per square meter for different materials
  const materialPrices: { [key: string]: number } = {
    'hout': 45,
    'tegels': 65,
    'aluminium': 85,
    'glas': 120,
    'staal': 95,
    'kunststof': 55,
    'beton': 35,
    'marmer': 150
  };
  
  // Labor rates per square meter
  const laborRates: { [key: string]: number } = {
    'hout': 25,
    'tegels': 35,
    'aluminium': 40,
    'glas': 50,
    'staal': 45,
    'kunststof': 30,
    'beton': 20,
    'marmer': 60
  };
  
  dimensions.forEach((dimension, index) => {
    const area = dimension.length * dimension.width;
    const volume = area * dimension.height;
    
    if (area > 0) {
      // Calculate for each material
      materials.forEach((material, materialIndex) => {
        const materialPrice = materialPrices[material] || 50;
        const laborRate = laborRates[material] || 30;
        
        const totalPrice = (materialPrice + laborRate) * area;
        
        items.push({
          id: `item-${index}-${materialIndex}`,
          description: `${material.charAt(0).toUpperCase() + material.slice(1)} werk - ${dimension.description || `${dimension.length}m x ${dimension.width}m`}`,
          quantity: dimension.quantity,
          unitPrice: materialPrice + laborRate,
          total: totalPrice * dimension.quantity
        });
      });
    }
  });
  
  return items;
};

// Mock GPT description generation
const generateDescription = (title: string, materials: string[], items: QuoteItem[]): string => {
  const totalAmount = items.reduce((sum, item) => sum + item.total, 0);
  const materialList = materials.join(', ');
  
  return `Professionale ${title.toLowerCase()} met hoogwaardige materialen (${materialList}). 
  Het project omvat alle benodigde materialen, professionele installatie en garantie. 
  De werkzaamheden worden uitgevoerd volgens de hoogste standaarden met aandacht voor detail en duurzaamheid. 
  Totaal projectwaarde: €${totalAmount.toFixed(2)}.`;
};

// Main AI Quote generation function
export const generateAIQuote = async (request: AIQuoteRequest): Promise<AIQuoteResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Step 1: Photo analysis - detect materials
  const materials = detectMaterials(request.photos);
  
  // Step 2: Calculate costs based on dimensions and materials
  const items = calculateCosts(request.dimensions, materials);
  
  // Step 3: Generate description
  const description = generateDescription(request.title, materials, items);
  
  // Step 4: Calculate totals
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);
  const vat = subtotal * 0.21; // 21% VAT
  const total = subtotal + vat;
  
  return {
    title: request.title,
    items,
    subtotal,
    vat,
    total,
    description
  };
};

// API endpoint handler (for use in a real backend)
export const handleAIQuoteRequest = async (request: AIQuoteRequest): Promise<AIQuoteResponse> => {
  try {
    return await generateAIQuote(request);
  } catch (error) {
    console.error('AI Quote generation error:', error);
    throw new Error('Failed to generate AI quote');
  }
}; 