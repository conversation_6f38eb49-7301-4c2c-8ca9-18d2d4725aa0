// src/api/dashboard.ts
// Simple mock service for dashboard KPI statistics.
// In a real application this would call your backend (e.g. REST/GraphQL).

import type { RevenueDataPoint, QuoteStatusDataPoint, CustomerGrowthDataPoint } from '../types/dashboard';

// A mock function to simulate network latency
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// --- KPI Cards Data --- //

export interface KpiStats {
  totalQuotes: { current: number; previous: number };
  revenue: { current: number; previous: number };
  newCustomers: { current: number; previous: number };
  conversionRate: { current: number; previous: number };
}

export async function getKpiStats(): Promise<KpiStats> {
  await sleep(500);
  return {
    totalQuotes: { current: 125, previous: 110 },
    revenue: { current: 75000, previous: 68000 },
    newCustomers: { current: 15, previous: 12 },
    conversionRate: { current: 0.68, previous: 0.65 },
  };
}

// --- Chart Data --- //

export const getRevenueTrend = async (period: '6m' | '12m' = '12m'): Promise<RevenueDataPoint[]> => {
  await sleep(800);
  const data: RevenueDataPoint[] = [
    { month: 'Jan', revenue: 65000 },
    { month: 'Feb', revenue: 68000 },
    { month: 'Mar', revenue: 72000 },
    { month: 'Apr', revenue: 71000 },
    { month: 'May', revenue: 75000 },
    { month: 'Jun', revenue: 78000 },
    { month: 'Jul', revenue: 81000 },
    { month: 'Aug', revenue: 85000 },
    { month: 'Sep', revenue: 88000 },
    { month: 'Oct', revenue: 91000 },
    { month: 'Nov', revenue: 95000 },
    { month: 'Dec', revenue: 98000 },
  ];
  return period === '6m' ? data.slice(-6) : data;
};

export const getQuoteStatus = async (): Promise<QuoteStatusDataPoint[]> => {
  await sleep(600);
  return [
    { name: 'Accepted', value: 85, percentage: 68, color: '#34D399' },
    { name: 'Pending', value: 25, percentage: 20, color: '#FBBF24' },
    { name: 'Rejected', value: 15, percentage: 12, color: '#F87171' },
  ];
};

export const getCustomerGrowth = async (period: '6m' | '12m' = '12m'): Promise<CustomerGrowthDataPoint[]> => {
  await sleep(700);
  const data: CustomerGrowthDataPoint[] = [
    { month: 'Jan', new: 10, total: 150 },
    { month: 'Feb', new: 12, total: 162 },
    { month: 'Mar', new: 15, total: 177 },
    { month: 'Apr', new: 14, total: 191 },
    { month: 'May', new: 18, total: 209 },
    { month: 'Jun', new: 20, total: 229 },
    { month: 'Jul', new: 22, total: 251 },
    { month: 'Aug', new: 25, total: 276 },
    { month: 'Sep', new: 28, total: 304 },
    { month: 'Oct', new: 30, total: 334 },
    { month: 'Nov', new: 32, total: 366 },
    { month: 'Dec', new: 35, total: 401 },
  ];
  return period === '6m' ? data.slice(-6) : data;
};
