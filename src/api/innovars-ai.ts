import { api, withRetry } from './client';

// Innovars AI API types
export interface VoiceMessage {
  audioBlob: Blob;
  duration: number;
  language: 'nl' | 'fr' | 'en';
}

export interface AIResponse {
  response: string;
  confidence: number;
  suggestions: string[];
  intent: 'quote_creation' | 'quote_update' | 'customer_info' | 'general_query' | 'market_analysis';
  context?: {
    customerData?: any;
    projectData?: any;
    marketData?: any;
  };
}

export interface ProcessTextMessageRequest {
  message: string;
  context?: 'quote_generation' | 'customer_management' | 'market_analysis' | 'general';
  language?: 'nl' | 'fr' | 'en';
  userId?: string;
}

export interface QuoteSuggestion {
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    category: string;
    materiaalKosten?: number;
    arbeidKosten?: number;
  }>;
  totalAmount: number;
  btw: number;
  reasoning: string;
  energyRating?: string;
  sustainability?: {
    co2Impact: number;
    energySavings: number;
    materials: string[];
  };
}

export interface PriceOptimization {
  currentPrice: number;
  suggestedPrice: number;
  marketAverage: number;
  reasoning: string;
  confidence: number;
  regionalFactors?: {
    location: string;
    priceIndex: number;
    laborCosts: number;
  };
}

export interface MarketInsight {
  sector: string;
  region: string;
  trends: Array<{
    category: string;
    trend: 'up' | 'down' | 'stable';
    percentage: number;
    reasoning: string;
  }>;
  pricing: {
    average: number;
    low: number;
    high: number;
  };
  forecast: {
    nextQuarter: string;
    confidence: number;
  };
}

export interface InnovarsAIStatus {
  online: boolean;
  version: string;
  capabilities: string[];
  responseTime: number;
  accuracy: number;
  lastUpdated: Date;
  features: {
    voiceRecognition: boolean;
    imageAnalysis: boolean;
    priceOptimization: boolean;
    marketAnalysis: boolean;
    multiLanguage: boolean;
  };
}

// Innovars AI API services
export const innovarsAIApi = {
  // Process voice message
  processVoiceMessage: async (voiceMessage: VoiceMessage) => {
    const formData = new FormData();
    formData.append('audio', voiceMessage.audioBlob);
    formData.append('duration', voiceMessage.duration.toString());
    formData.append('language', voiceMessage.language);

    return withRetry(() => api.post<AIResponse>('/innovars-ai/voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }));
  },

  // Process text message
  processTextMessage: async (request: ProcessTextMessageRequest) => {
    // Mock response for development
    const mockResponse: AIResponse = {
      response: `Bedankt voor uw bericht!

Ik begrijp dat u geïnteresseerd bent in een project.

Ik kan u helpen met:
✅ Automatische prijsberekeningen
✅ Materiaallijsten
✅ Energiebesparende opties
✅ Planning en tijdlijnen`,
      confidence: 0.95,
      suggestions: [
        'Voeg meer projectdetails toe',
        'Vraag om een prijsberekening',
        'Bekijk vergelijkbare projecten',
        'Plan een locatiebezoek in'
      ],
      intent: request.context === 'quote_generation' ? 'quote_creation' : 'general_query',
      context: {
        projectData: {
          type: 'inferred_from_message',
          stage: 'initial_inquiry'
        }
      }
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    return { data: mockResponse };

    // Real API call would be:
    // return withRetry(() => api.post<AIResponse>('/innovars-ai/text', request));
  },

  // Generate quote suggestions
  generateQuoteSuggestions: async (projectDescription: string, customerInfo?: any) => {
    return withRetry(() => api.post<QuoteSuggestion>('/innovars-ai/suggestions', {
      projectDescription,
      customerInfo,
      source: 'innovars_ai'
    }));
  },

  // Optimize pricing with Innovars intelligence
  optimizePricing: async (quoteItems: any[], marketData?: any) => {
    return withRetry(() => api.post<PriceOptimization>('/innovars-ai/optimize-pricing', {
      quoteItems,
      marketData,
      algorithm: 'innovars_v2'
    }));
  },

  // Get Innovars AI status
  getStatus: async () => {
    const mockStatus: InnovarsAIStatus = {
      online: true,
      version: '2.1.0',
      capabilities: [
        'voice_recognition',
        'text_processing',
        'quote_generation',
        'price_optimization',
        'market_analysis',
        'multi_language'
      ],
      responseTime: 850,
      accuracy: 0.94,
      lastUpdated: new Date(),
      features: {
        voiceRecognition: true,
        imageAnalysis: true,
        priceOptimization: true,
        marketAnalysis: true,
        multiLanguage: true
      }
    };

    await new Promise(resolve => setTimeout(resolve, 500));
    return { data: mockStatus };
  },

  // Get conversation history
  getConversationHistory: async (limit: number = 50) => {
    return withRetry(() => api.get(`/innovars-ai/conversations?limit=${limit}`));
  },

  // Save conversation
  saveConversation: async (conversation: {
    messages: Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date }>;
    summary: string;
    projectContext?: any;
  }) => {
    return withRetry(() => api.post('/innovars-ai/conversations', {
      ...conversation,
      aiVersion: 'innovars_ai_v2'
    }));
  },

  // Get Innovars AI insights
  getAIInsights: async () => {
    return withRetry(() => api.get('/innovars-ai/insights'));
  },

  // Provide feedback for Innovars AI improvement
  provideFeedback: async (conversationId: string, feedback: {
    rating: number;
    comment?: string;
    corrections?: string[];
    helpful?: boolean;
  }) => {
    return withRetry(() => api.post(`/innovars-ai/feedback/${conversationId}`, {
      ...feedback,
      aiVersion: 'innovars_ai_v2'
    }));
  },

  // Get market analysis with Innovars intelligence
  getMarketAnalysis: async (location: string, sector: string) => {
    return withRetry(() => api.get(`/innovars-ai/market-analysis?location=${encodeURIComponent(location)}&sector=${encodeURIComponent(sector)}&provider=innovars`));
  },

  // Generate project templates with Innovars standards
  generateProjectTemplates: async (projectType: string, location: string) => {
    return withRetry(() => api.get(`/innovars-ai/templates?projectType=${encodeURIComponent(projectType)}&location=${encodeURIComponent(location)}&standard=innovars`));
  },

  // Validate quote completeness with Innovars standards
  validateQuote: async (quoteData: any) => {
    return withRetry(() => api.post('/innovars-ai/validate-quote', {
      ...quoteData,
      validationStandard: 'innovars_professional'
    }));
  },

  // Get energy improvement suggestions
  getEnergySuggestions: async (projectDetails: any) => {
    return withRetry(() => api.post('/innovars-ai/energy-suggestions', {
      ...projectDetails,
      complianceStandard: 'dutch_energy_2024'
    }));
  },

  // Advanced price prediction
  predictPricing: async (projectDetails: any, historicalData?: any) => {
    return withRetry(() => api.post('/innovars-ai/predict-pricing', {
      projectDetails,
      historicalData,
      model: 'innovars_price_predictor_v2'
    }));
  },

  // Generate smart recommendations
  getSmartRecommendations: async (context: {
    projectType: string;
    budget: number;
    location: string;
    preferences?: string[];
  }) => {
    return withRetry(() => api.post('/innovars-ai/smart-recommendations', {
      ...context,
      aiEngine: 'innovars_recommendation_engine'
    }));
  },

  // Process WhatsApp integration
  processWhatsAppMessage: async (message: string, phoneNumber: string) => {
    return withRetry(() => api.post('/innovars-ai/whatsapp', {
      message,
      phoneNumber,
      aiProvider: 'innovars'
    }));
  },

  // Get competitive analysis
  getCompetitiveAnalysis: async (projectType: string, location: string) => {
    return withRetry(() => api.post('/innovars-ai/competitive-analysis', {
      projectType,
      location,
      analysisEngine: 'innovars_market_intelligence'
    }));
  },

  // Quality assurance check
  performQualityCheck: async (quoteData: any) => {
    return withRetry(() => api.post('/innovars-ai/quality-check', {
      ...quoteData,
      qualityStandard: 'innovars_premium'
    }));
  }
};