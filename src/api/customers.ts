import { api, withRetry } from './client';
import { Customer } from '@/stores/useAppStore';

// Customer API types
export interface CreateCustomerRequest {
  name: string;
  email: string;
  phone: string;
  company: string;
  address: string;
}

export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;
  status?: Customer['status'];
}

export interface CustomerFilters {
  status?: Customer['status'];
  search?: string;
  company?: string;
}

// Customer API services
export const customerApi = {
  // Get all customers with optional filters
  getCustomers: async (filters?: CustomerFilters) => {
    const params = new URLSearchParams();
    
    if (filters?.status) {params.append('status', filters.status);}
    if (filters?.search) {params.append('search', filters.search);}
    if (filters?.company) {params.append('company', filters.company);}

    const queryString = params.toString();
    const url = queryString ? `/customers?${queryString}` : '/customers';
    
    return withRetry(() => api.get<Customer[]>(url));
  },

  // Get customer by ID
  getCustomer: async (id: string) => {
    return withRetry(() => api.get<Customer>(`/customers/${id}`));
  },

  // Create new customer
  createCustomer: async (data: CreateCustomerRequest) => {
    return withRetry(() => api.post<Customer>('/customers', data));
  },

  // Update customer
  updateCustomer: async (id: string, data: UpdateCustomerRequest) => {
    return withRetry(() => api.put<Customer>(`/customers/${id}`, data));
  },

  // Delete customer
  deleteCustomer: async (id: string) => {
    return withRetry(() => api.delete(`/customers/${id}`));
  },

  // Update customer status
  updateCustomerStatus: async (id: string, status: Customer['status']) => {
    return withRetry(() => api.patch<Customer>(`/customers/${id}/status`, { status }));
  },

  // Search customers
  searchCustomers: async (query: string) => {
    return withRetry(() => api.get<Customer[]>(`/customers/search?q=${encodeURIComponent(query)}`));
  },

  // Get customer statistics
  getCustomerStats: async () => {
    return withRetry(() => api.get('/customers/stats'));
  },

  // Import customers from CSV
  importCustomers: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return withRetry(() => api.post('/customers/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }));
  },

  // Export customers to CSV
  exportCustomers: async (filters?: CustomerFilters) => {
    const params = new URLSearchParams();
    
    if (filters?.status) {params.append('status', filters.status);}
    if (filters?.search) {params.append('search', filters.search);}
    if (filters?.company) {params.append('company', filters.company);}

    const queryString = params.toString();
    const url = queryString ? `/customers/export?${queryString}` : '/customers/export';
    
    return withRetry(() => api.get(url, {
      responseType: 'blob',
    }));
  },

  // Get customer activity
  getCustomerActivity: async (id: string) => {
    return withRetry(() => api.get(`/customers/${id}/activity`));
  },

  // Get customer notes
  getCustomerNotes: async (id: string) => {
    return withRetry(() => api.get(`/customers/${id}/notes`));
  },

  // Add note to customer
  addCustomerNote: async (id: string, note: string) => {
    return withRetry(() => api.post(`/customers/${id}/notes`, { note }));
  },
}; 