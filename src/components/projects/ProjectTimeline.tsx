import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Circle,
  User,
  MapPin,
  Phone,
  Mail,
  Plus,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Modal } from '@/components/ui/Modal';
import { FormInput, FormSelect, FormTextarea } from '@/components/ui/Form';
import { useToast } from '@/contexts/ToastContext';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, isToday } from 'date-fns';
import { nl } from 'date-fns/locale';

export interface ProjectTask {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'delayed';
  priority: 'low' | 'medium' | 'high';
  assignedTo: string;
  estimatedHours: number;
  actualHours?: number;
  dependencies: string[]; // Task IDs that must be completed first
  category: 'preparation' | 'demolition' | 'construction' | 'installation' | 'finishing' | 'cleanup';
}

export interface Project {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  status: 'planning' | 'active' | 'completed' | 'on-hold';
  customerId: string;
  customerName: string;
  address: string;
  tasks: ProjectTask[];
  budget: number;
  actualCost?: number;
}

const statusColors = {
  pending: 'bg-gray-500',
  'in-progress': 'bg-blue-500',
  completed: 'bg-green-500',
  delayed: 'bg-red-500',
};

const statusLabels = {
  pending: 'Wachtend',
  'in-progress': 'Bezig',
  completed: 'Voltooid',
  delayed: 'Vertraagd',
};

const priorityColors = {
  low: 'text-green-400',
  medium: 'text-yellow-400',
  high: 'text-red-400',
};

const categoryLabels = {
  preparation: 'Voorbereiding',
  demolition: 'Sloop',
  construction: 'Bouw',
  installation: 'Installatie',
  finishing: 'Afwerking',
  cleanup: 'Opruimen',
};

// Mock project data
const mockProject: Project = {
  id: '1',
  name: 'Badkamer Renovatie - Familie Jansen',
  description: 'Complete badkamer renovatie inclusief nieuwe tegels, sanitair en leidingwerk',
  startDate: new Date('2024-02-01'),
  endDate: new Date('2024-02-28'),
  status: 'active',
  customerId: '1',
  customerName: 'Familie Jansen',
  address: 'Hoofdstraat 123, 1234 AB Amsterdam',
  budget: 15000,
  actualCost: 8500,
  tasks: [
    {
      id: '1',
      title: 'Materialen bestellen',
      description: 'Tegels, sanitair en leidingmateriaal bestellen',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-02-02'),
      status: 'completed',
      priority: 'high',
      assignedTo: 'Jan de Vries',
      estimatedHours: 4,
      actualHours: 3,
      dependencies: [],
      category: 'preparation',
    },
    {
      id: '2',
      title: 'Oude badkamer slopen',
      description: 'Verwijderen van oude tegels, sanitair en leidingen',
      startDate: new Date('2024-02-05'),
      endDate: new Date('2024-02-07'),
      status: 'completed',
      priority: 'medium',
      assignedTo: 'Piet Bakker',
      estimatedHours: 24,
      actualHours: 26,
      dependencies: ['1'],
      category: 'demolition',
    },
    {
      id: '3',
      title: 'Leidingwerk aanleggen',
      description: 'Nieuwe water- en afvoerleidingen installeren',
      startDate: new Date('2024-02-08'),
      endDate: new Date('2024-02-12'),
      status: 'in-progress',
      priority: 'high',
      assignedTo: 'Klaas Mulder',
      estimatedHours: 32,
      dependencies: ['2'],
      category: 'installation',
    },
    {
      id: '4',
      title: 'Tegels plaatsen',
      description: 'Wand- en vloertegels plaatsen',
      startDate: new Date('2024-02-13'),
      endDate: new Date('2024-02-18'),
      status: 'pending',
      priority: 'medium',
      assignedTo: 'Jan de Vries',
      estimatedHours: 40,
      dependencies: ['3'],
      category: 'construction',
    },
    {
      id: '5',
      title: 'Sanitair installeren',
      description: 'Toilet, wastafel en douche installeren',
      startDate: new Date('2024-02-19'),
      endDate: new Date('2024-02-22'),
      status: 'pending',
      priority: 'high',
      assignedTo: 'Klaas Mulder',
      estimatedHours: 24,
      dependencies: ['4'],
      category: 'installation',
    },
    {
      id: '6',
      title: 'Afwerking en oplevering',
      description: 'Laatste afwerking en schoonmaken',
      startDate: new Date('2024-02-26'),
      endDate: new Date('2024-02-28'),
      status: 'pending',
      priority: 'low',
      assignedTo: 'Jan de Vries',
      estimatedHours: 16,
      dependencies: ['5'],
      category: 'finishing',
    },
  ],
};

interface ProjectTimelineProps {
  project?: Project;
  onUpdateTask?: (taskId: string, updates: Partial<ProjectTask>) => void;
  onAddTask?: (task: Omit<ProjectTask, 'id'>) => void;
  onDeleteTask?: (taskId: string) => void;
}

export const ProjectTimeline: React.FC<ProjectTimelineProps> = ({
  project = mockProject,
  onUpdateTask,
  onAddTask,
  onDeleteTask,
}) => {
  const { showToast } = useToast();
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [viewMode, setViewMode] = useState<'timeline' | 'calendar' | 'list'>('timeline');
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [editingTask, setEditingTask] = useState<ProjectTask | null>(null);

  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 });
  const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const getTasksForDay = (date: Date) => {
    return project.tasks.filter(task => {
      const taskStart = new Date(task.startDate);
      const taskEnd = new Date(task.endDate);
      return date >= taskStart && date <= taskEnd;
    });
  };

  const getTaskProgress = (task: ProjectTask) => {
    if (task.status === 'completed') return 100;
    if (task.status === 'pending') return 0;
    
    const now = new Date();
    const start = new Date(task.startDate);
    const end = new Date(task.endDate);
    const total = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();
    
    return Math.max(0, Math.min(100, (elapsed / total) * 100));
  };

  const updateTaskStatus = (taskId: string, status: ProjectTask['status']) => {
    if (onUpdateTask) {
      onUpdateTask(taskId, { status });
    }
    
    showToast({
      type: 'success',
      title: 'Status Bijgewerkt',
      message: `Taak status is bijgewerkt naar ${statusLabels[status]}.`,
    });
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek(prev => addDays(prev, direction === 'next' ? 7 : -7));
  };

  const renderTimelineView = () => (
    <div className="space-y-4">
      {/* Week Navigation */}
      <div className="flex items-center justify-between bg-glass-light backdrop-blur-md border border-white/20 rounded-lg p-4">
        <Button variant="ghost" onClick={() => navigateWeek('prev')}>
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">
          {format(weekStart, 'dd MMM', { locale: nl })} - {format(weekEnd, 'dd MMM yyyy', { locale: nl })}
        </h3>
        <Button variant="ghost" onClick={() => navigateWeek('next')}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Timeline Grid */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-lg overflow-hidden">
        {/* Header */}
        <div className="grid grid-cols-8 border-b border-white/20">
          <div className="p-4 font-medium text-white">Taak</div>
          {weekDays.map((day) => (
            <div
              key={day.toISOString()}
              className={`p-4 text-center font-medium ${
                isToday(day) ? 'bg-blue-500/20 text-blue-300' : 'text-gray-300'
              }`}
            >
              <div className="text-sm">{format(day, 'EEE', { locale: nl })}</div>
              <div className="text-lg">{format(day, 'd')}</div>
            </div>
          ))}
        </div>

        {/* Tasks */}
        {project.tasks.map((task) => (
          <div key={task.id} className="grid grid-cols-8 border-b border-white/20 hover:bg-white/5">
            <div className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${statusColors[task.status]}`} />
                <div>
                  <p className="text-white font-medium text-sm">{task.title}</p>
                  <p className="text-gray-400 text-xs">{task.assignedTo}</p>
                </div>
              </div>
            </div>
            {weekDays.map((day) => {
              const hasTask = getTasksForDay(day).some(t => t.id === task.id);
              return (
                <div
                  key={day.toISOString()}
                  className={`p-2 ${hasTask ? 'bg-blue-500/20' : ''}`}
                >
                  {hasTask && (
                    <div className={`h-6 rounded ${statusColors[task.status]} opacity-80`} />
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );

  const renderListView = () => (
    <div className="space-y-4">
      {project.tasks.map((task) => (
        <Card key={task.id} className="bg-glass-light backdrop-blur-md border border-white/20 p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <div className={`w-4 h-4 rounded-full ${statusColors[task.status]}`} />
                <h3 className="text-lg font-semibold text-white">{task.title}</h3>
                <span className={`text-sm font-medium ${priorityColors[task.priority]}`}>
                  {task.priority.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-300 mb-4">{task.description}</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-400">Toegewezen aan</p>
                  <p className="text-white font-medium">{task.assignedTo}</p>
                </div>
                <div>
                  <p className="text-gray-400">Startdatum</p>
                  <p className="text-white font-medium">
                    {format(task.startDate, 'dd MMM yyyy', { locale: nl })}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400">Einddatum</p>
                  <p className="text-white font-medium">
                    {format(task.endDate, 'dd MMM yyyy', { locale: nl })}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400">Voortgang</p>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${statusColors[task.status]}`}
                        style={{ width: `${getTaskProgress(task)}%` }}
                      />
                    </div>
                    <span className="text-white text-xs">{Math.round(getTaskProgress(task))}%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-4">
              <FormSelect
                value={task.status}
                onChange={(e) => updateTaskStatus(task.id, e.target.value as ProjectTask['status'])}
                options={[
                  { value: 'pending', label: 'Wachtend' },
                  { value: 'in-progress', label: 'Bezig' },
                  { value: 'completed', label: 'Voltooid' },
                  { value: 'delayed', label: 'Vertraagd' },
                ]}
                className="w-32"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingTask(task)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteTask?.(task.id)}
                className="text-red-400 hover:text-red-300"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">{project.name}</h1>
          <p className="text-gray-300">{project.description}</p>
        </div>
        <div className="flex items-center space-x-3">
          <FormSelect
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value as any)}
            options={[
              { value: 'timeline', label: 'Timeline' },
              { value: 'list', label: 'Lijst' },
            ]}
          />
          <Button onClick={() => setShowTaskModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nieuwe Taak
          </Button>
        </div>
      </div>

      {/* Project Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-glass-light backdrop-blur-md border border-white/20 p-6">
          <div className="flex items-center space-x-3">
            <Calendar className="h-8 w-8 text-blue-400" />
            <div>
              <p className="text-sm text-gray-400">Project Periode</p>
              <p className="text-white font-semibold">
                {format(project.startDate, 'dd MMM', { locale: nl })} - {format(project.endDate, 'dd MMM yyyy', { locale: nl })}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="bg-glass-light backdrop-blur-md border border-white/20 p-6">
          <div className="flex items-center space-x-3">
            <User className="h-8 w-8 text-green-400" />
            <div>
              <p className="text-sm text-gray-400">Klant</p>
              <p className="text-white font-semibold">{project.customerName}</p>
            </div>
          </div>
        </Card>
        
        <Card className="bg-glass-light backdrop-blur-md border border-white/20 p-6">
          <div className="flex items-center space-x-3">
            <MapPin className="h-8 w-8 text-purple-400" />
            <div>
              <p className="text-sm text-gray-400">Locatie</p>
              <p className="text-white font-semibold">{project.address}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* View Content */}
      {viewMode === 'timeline' ? renderTimelineView() : renderListView()}
    </div>
  );
};
