import React, { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff, Volume2, VolumeX, RotateCcw, Send } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';

interface VoiceInputProps {
  onTranscript?: (text: string) => void;
  onSend?: (text: string) => void;
  placeholder?: string;
  disabled?: boolean;
  autoSend?: boolean;
  language?: string;
  className?: string;
}

export const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  onSend,
  placeholder = 'Spreek om te beginnen...',
  disabled = false,
  autoSend = false,
  language = 'nl-NL',
  className,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(false);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if speech recognition is supported
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    setIsSupported(!!SpeechRecognition);
    
    if (SpeechRecognition) {
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = language;
      
      recognition.onstart = () => {
        setIsRecording(true);
        setError(null);
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript) {
          const newTranscript = transcript + finalTranscript;
          setTranscript(newTranscript);
          setInterimTranscript('');
          onTranscript?.(newTranscript);
          
          if (autoSend) {
            handleSend(newTranscript);
          }
        } else {
          setInterimTranscript(interimTranscript);
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsRecording(false);
        
        switch (event.error) {
          case 'not-allowed':
            setError('Microfoon toegang geweigerd');
            break;
          case 'no-speech':
            setError('Geen spraak gedetecteerd');
            break;
          case 'network':
            setError('Netwerk fout');
            break;
          default:
            setError('Spraakherkenning fout');
        }
      };

      recognition.onend = () => {
        setIsRecording(false);
        setInterimTranscript('');
      };

      recognitionRef.current = recognition;
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [language, onTranscript, autoSend]);

  // Start recording
  const startRecording = () => {
    if (!isSupported || disabled) {return;}
    
    try {
      setTranscript('');
      setInterimTranscript('');
      setError(null);
      recognitionRef.current?.start();
    } catch (err) {
      setError('Kan spraakherkenning niet starten');
      console.error('Error starting speech recognition:', err);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  // Toggle recording
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  // Clear transcript
  const clearTranscript = () => {
    setTranscript('');
    setInterimTranscript('');
    onTranscript?.('');
  };

  // Send transcript
  const handleSend = (text: string = transcript) => {
    if (text.trim()) {
      onSend?.(text.trim());
      if (!autoSend) {
        clearTranscript();
      }
    }
  };

  // Auto-stop after silence
  useEffect(() => {
    if (isRecording && interimTranscript) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        if (isRecording) {
          stopRecording();
        }
      }, 3000); // Stop after 3 seconds of silence
    }
  }, [interimTranscript, isRecording]);

  if (!isSupported) {
    return (
      <div className={cn(
        'p-4 bg-red-500/10 border border-red-500/30 rounded-lg',
        className
      )}>
        <p className="text-red-300 text-sm">
          Spraakherkenning wordt niet ondersteund in deze browser
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Voice Input Interface */}
      <div className="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-4">
        {/* Recording Status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-3 h-3 rounded-full',
              isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
            )} />
            <span className="text-white text-sm">
              {isRecording ? 'Opname actief' : 'Klaar voor opname'}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {isRecording && (
              <span className="text-white/60 text-xs">
                {interimTranscript ? 'Luistert...' : 'Wacht op spraak...'}
              </span>
            )}
          </div>
        </div>

        {/* Transcript Display */}
        <div className="min-h-[80px] p-3 bg-white/5 rounded-lg border border-white/10">
          {transcript || interimTranscript ? (
            <div className="space-y-2">
              {transcript && (
                <p className="text-white text-sm">{transcript}</p>
              )}
              {interimTranscript && (
                <p className="text-white/60 text-sm italic">
                  {interimTranscript}
                </p>
              )}
            </div>
          ) : (
            <p className="text-white/40 text-sm">{placeholder}</p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-3 p-2 bg-red-500/10 border border-red-500/30 rounded text-red-300 text-xs">
            {error}
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center gap-2">
            <Button
              onClick={toggleRecording}
              disabled={disabled}
              className={cn(
                'rounded-full w-12 h-12 p-0',
                isRecording 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-primary-500 hover:bg-primary-600 text-white'
              )}
            >
              {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
            </Button>
            
            {transcript && (
              <Button
                onClick={clearTranscript}
                variant="ghost"
                size="sm"
                className="text-white/60 hover:text-white"
              >
                <RotateCcw size={16} />
              </Button>
            )}
          </div>

          {transcript && !autoSend && (
            <Button
              onClick={() => handleSend()}
              size="sm"
              className="bg-primary-500 hover:bg-primary-600"
            >
              <Send size={16} className="mr-1" />
              Verstuur
            </Button>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="text-xs text-white/40 space-y-1">
        <p>• Klik op de microfoon om te beginnen met opnemen</p>
        <p>• Spreek duidelijk en in normale snelheid</p>
        <p>• De opname stopt automatisch na 3 seconden stilte</p>
        {autoSend && <p>• Transcript wordt automatisch verzonden</p>}
      </div>
    </div>
  );
}; 