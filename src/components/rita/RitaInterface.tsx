import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Mic<PERSON>ff, 
  Send, 
  MessageSquare,
  Bot,
  User,
  Loader2,
  Volume2,
  VolumeX,
  Settings,
  Download,
  Copy
} from 'lucide-react';
import { useVoiceRecognition } from '@/hooks/useVoiceRecognition';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@/components/ui/Button';
import { FormInput } from '@/components/ui/Form';
import { ritaApi } from '@/api/rita';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isProcessing?: boolean;
}

export const RitaInterface: React.FC = () => {
  const { 
    isRecording, 
    isProcessing, 
    startRecording, 
    stopRecording, 
    transcription,
    error: voiceError 
  } = useVoiceRecognition();
  
  const { showToast } = useToast();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle voice transcription
  useEffect(() => {
    if (transcription && !isProcessing) {
      setInputMessage(transcription);
    }
  }, [transcription, isProcessing]);

  // Handle voice errors
  useEffect(() => {
    if (voiceError) {
      showToast({
        type: 'error',
        title: 'Spraakherkenning Fout',
        message: voiceError,
      });
    }
  }, [voiceError, showToast]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isSending) {return;}

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    const aiMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'ai',
      content: '',
      timestamp: new Date(),
      isProcessing: true,
    };

    setMessages(prev => [...prev, userMessage, aiMessage]);
    setInputMessage('');
    setIsSending(true);

    try {
      // Process text message with Rita AI
      const response = await ritaApi.processTextMessage({
        message: userMessage.content,
        context: 'quote_generation',
      });

      // Update AI message with response
      setMessages(prev => 
        prev.map(msg => 
          msg.id === aiMessage.id 
            ? { ...msg, content: response.response, isProcessing: false }
            : msg
        )
      );

      showToast({
        type: 'success',
        title: 'Rita AI',
        message: 'Bericht verwerkt en antwoord ontvangen.',
      });
    } catch (error) {
      // Update AI message with error
      setMessages(prev => 
        prev.map(msg => 
          msg.id === aiMessage.id 
            ? { ...msg, content: 'Sorry, er is een fout opgetreden. Probeer het opnieuw.', isProcessing: false }
            : msg
        )
      );

      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het verwerken van uw bericht.',
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleVoiceToggle = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    showToast({
      type: 'success',
      title: 'Gekopieerd',
      message: 'Bericht gekopieerd naar klembord.',
    });
  };

  const handleExportChat = () => {
    const chatText = messages
      .map(msg => `${msg.type === 'user' ? 'U' : 'Rita'}: ${msg.content}`)
      .join('\n\n');
    
    const blob = new Blob([chatText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rita-chat-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);

    showToast({
      type: 'success',
      title: 'Geëxporteerd',
      message: 'Chat geëxporteerd naar bestand.',
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-glass-light backdrop-blur-md border-b border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">Rita AI Assistant</h1>
              <p className="text-sm text-gray-300">Uw AI assistent voor offerte generatie</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMuted(!isMuted)}
              title={isMuted ? 'Geluid aanzetten' : 'Geluid uitzetten'}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportChat}
              title="Chat exporteren"
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              title="Instellingen"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <Bot className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Welkom bij Rita AI</h3>
            <p className="text-gray-400 max-w-md mx-auto">
              Stel uw vragen over offerte generatie, prijsberekening, of bouwprojecten. 
              Rita helpt u snel en efficiënt verder.
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-4 ${
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-glass-dark text-white'
                }`}
              >
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0">
                    {message.type === 'user' ? (
                      <User className="h-5 w-5" />
                    ) : (
                      <Bot className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1">
                    {message.isProcessing ? (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-gray-300">Rita denkt na...</span>
                      </div>
                    ) : (
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    )}
                    <div className="text-xs text-gray-400 mt-2">
                      {message.timestamp.toLocaleTimeString('nl-NL', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                  {!message.isProcessing && (
                    <button
                      onClick={() => handleCopyMessage(message.content)}
                      className="flex-shrink-0 text-gray-400 hover:text-white transition-colors"
                      title="Kopieer bericht"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="bg-glass-light backdrop-blur-md border-t border-white/20 p-4">
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <FormInput
              placeholder="Typ uw bericht of vraag..."
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isSending}
              multiline
              rows={3}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={isRecording ? 'danger' : 'glass'}
              size="lg"
              onClick={handleVoiceToggle}
              disabled={isSending}
              title={isRecording ? 'Stop opname' : 'Start spraakherkenning'}
            >
              {isRecording ? (
                <MicOff className="h-5 w-5" />
              ) : (
                <Mic className="h-5 w-5" />
              )}
            </Button>
            
            <Button
              size="lg"
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isSending}
              title="Verstuur bericht"
            >
              {isSending ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
        
        {/* Voice Status */}
        {isRecording && (
          <div className="mt-3 flex items-center space-x-2 text-red-400">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
            <span className="text-sm">Spraakherkenning actief...</span>
          </div>
        )}
        
        {isProcessing && (
          <div className="mt-3 flex items-center space-x-2 text-blue-400">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Spraak wordt verwerkt...</span>
          </div>
        )}
      </div>
    </div>
  );
}; 