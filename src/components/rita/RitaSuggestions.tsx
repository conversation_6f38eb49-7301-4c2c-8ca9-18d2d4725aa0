import React from 'react';
import { 
  Lightbulb,
  Calculator,
  FileText,
  TrendingUp,
  Clock,
  Star,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface Suggestion {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: string;
  category: 'quick' | 'template' | 'insight';
}

const suggestions: Suggestion[] = [
  {
    id: '1',
    title: 'Offerte Genereren',
    description: 'Maak een nieuwe offerte aan met AI assistentie',
    icon: <FileText className="h-5 w-5" />,
    action: 'Genereer een offerte voor een renovatieproject',
    category: 'quick',
  },
  {
    id: '2',
    title: 'Prij<PERSON>berekening',
    description: 'Bereken prijzen voor bouwmaterialen en werk',
    icon: <Calculator className="h-5 w-5" />,
    action: 'Bereken de kosten voor een keukenrenovatie',
    category: 'quick',
  },
  {
    id: '3',
    title: '<PERSON>tanalyse',
    description: '<PERSON><PERSON><PERSON><PERSON> inzicht in huidige markttrends',
    icon: <TrendingUp className="h-5 w-5" />,
    action: 'Geef me een marktanalyse voor de bouwsector',
    category: 'insight',
  },
  {
    id: '4',
    title: 'Energie Advies',
    description: 'Vraag om energiebesparende suggesties',
    icon: <Zap className="h-5 w-5" />,
    action: 'Geef me advies over energiebesparing in woningen',
    category: 'insight',
  },
  {
    id: '5',
    title: 'Project Template',
    description: 'Gebruik een bestaand project als template',
    icon: <Star className="h-5 w-5" />,
    action: 'Toon me beschikbare project templates',
    category: 'template',
  },
  {
    id: '6',
    title: 'Planning',
    description: 'Plan projecten en deadlines',
    icon: <Clock className="h-5 w-5" />,
    action: 'Help me met projectplanning en tijdlijnen',
    category: 'quick',
  },
];

interface RitaSuggestionsProps {
  onSuggestionClick: (action: string) => void;
}

export const RitaSuggestions: React.FC<RitaSuggestionsProps> = ({
  onSuggestionClick,
}) => {
  const getCategoryColor = (category: Suggestion['category']) => {
    switch (category) {
      case 'quick':
        return 'border-blue-500/30 bg-blue-500/10';
      case 'template':
        return 'border-purple-500/30 bg-purple-500/10';
      case 'insight':
        return 'border-green-500/30 bg-green-500/10';
      default:
        return 'border-gray-500/30 bg-gray-500/10';
    }
  };

  const getCategoryIcon = (category: Suggestion['category']) => {
    switch (category) {
      case 'quick':
        return <Zap className="h-4 w-4 text-blue-400" />;
      case 'template':
        return <Star className="h-4 w-4 text-purple-400" />;
      case 'insight':
        return <Lightbulb className="h-4 w-4 text-green-400" />;
      default:
        return <Lightbulb className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Lightbulb className="h-6 w-6 text-yellow-400" />
        <div>
          <h2 className="text-lg font-semibold text-white">Rita's Suggesties</h2>
          <p className="text-sm text-gray-300">Snelle acties en templates</p>
        </div>
      </div>

      {/* Suggestions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {suggestions.map((suggestion) => (
          <div
            key={suggestion.id}
            className={`p-4 rounded-lg border ${getCategoryColor(suggestion.category)} hover:bg-white/5 transition-colors cursor-pointer`}
            onClick={() => onSuggestionClick(suggestion.action)}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-glass-dark rounded-lg flex items-center justify-center">
                  {suggestion.icon}
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  {getCategoryIcon(suggestion.category)}
                  <h3 className="text-sm font-medium text-white truncate">
                    {suggestion.title}
                  </h3>
                </div>
                <p className="text-xs text-gray-300 line-clamp-2">
                  {suggestion.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-4">
        <h3 className="text-sm font-medium text-white mb-3">Snelle Acties</h3>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="glass"
            size="sm"
            onClick={() => onSuggestionClick('Maak een offerte voor een badkamer renovatie')}
          >
            Badkamer Renovatie
          </Button>
          <Button
            variant="glass"
            size="sm"
            onClick={() => onSuggestionClick('Bereken kosten voor dakisolatie')}
          >
            Dakisolatie
          </Button>
          <Button
            variant="glass"
            size="sm"
            onClick={() => onSuggestionClick('Offerte voor zonnepanelen installatie')}
          >
            Zonnepanelen
          </Button>
          <Button
            variant="glass"
            size="sm"
            onClick={() => onSuggestionClick('Kostenberekening voor uitbouw')}
          >
            Uitbouw
          </Button>
        </div>
      </div>
    </div>
  );
}; 