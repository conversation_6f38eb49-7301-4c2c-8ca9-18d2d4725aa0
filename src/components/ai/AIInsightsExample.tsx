import React from 'react';
import { AIInsightsWidget } from './AIInsightsWidget';
import { useAIInsights } from '@/hooks/useAIInsights';

export const AIInsightsExample: React.FC = () => {
  const { insights, stats, markAllAsRead } = useAIInsights();

  return (
    <div className="space-y-6">
      <div className="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">AI Insights Widget Demo</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Widget Status</h3>
            <p className="text-white/60 text-sm">
              De AI Insights widget is nu beschikbaar op elke pagina als een floating widget in de rechteronderhoek.
            </p>
          </div>
          
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Features</h3>
            <ul className="text-white/60 text-sm space-y-1">
              <li>• Inklapbare floating widget</li>
              <li>• Real-time AI insights</li>
              <li>• Priority filtering</li>
              <li>• Read/unread status</li>
              <li>• Action buttons</li>
            </ul>
          </div>
          
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Stats</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-white/60">Totaal insights:</span>
                <span className="text-white">{stats.totalInsights}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Ongelezen:</span>
                <span className="text-white">{stats.unreadInsights}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Nieuwe leads:</span>
                <span className="text-white">{stats.newLeads}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-white font-medium">Huidige Insights</h3>
          <div className="space-y-2">
            {insights.map((insight) => (
              <div
                key={insight.id}
                className="p-3 bg-white/5 rounded-lg border border-white/10"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-white font-medium">{insight.title}</h4>
                    <p className="text-white/60 text-sm">{insight.description}</p>
                  </div>
                  <div className="text-right">
                    <span className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium',
                      insight.priority === 'high' && 'bg-red-500/20 text-red-300',
                      insight.priority === 'medium' && 'bg-yellow-500/20 text-yellow-300',
                      insight.priority === 'low' && 'bg-blue-500/20 text-blue-300'
                    )}>
                      {insight.priority}
                    </span>
                    {!insight.isRead && (
                      <div className="mt-1">
                        <span className="text-green-400 text-xs">Nieuw</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex gap-2">
            <button
              onClick={markAllAsRead}
              className="px-4 py-2 bg-primary-500 hover:bg-primary-600 rounded-lg text-white text-sm"
            >
              Markeer alles als gelezen
            </button>
          </div>
        </div>
      </div>

      <div className="text-center text-white/60 text-sm">
        <p>Kijk naar de rechteronderhoek voor de AI Insights widget!</p>
      </div>
    </div>
  );
};

// Helper function
const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
}; 