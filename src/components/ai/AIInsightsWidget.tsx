import React, { useState } from 'react';
import { 
  Ch<PERSON>ronDown, 
  Star, 
  Eye, 
  Target, 
  TrendingUp, 
  Zap,
  Clock,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { Card, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';
import { useAIInsights } from '@/hooks/useAIInsights';

// Icon mapping
const iconMap: Record<string, React.ReactNode> = {
  Eye: <Eye size={16} />,
  Target: <Target size={16} />,
  TrendingUp: <TrendingUp size={16} />,
  Zap: <Zap size={16} />,
};

interface AIInsightsWidgetProps {
  className?: string;
}

export const AIInsightsWidget: React.FC<AIInsightsWidgetProps> = ({ className }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  
  const { 
    insights, 
    stats, 
    loading, 
    error, 
    markAsRead, 
    getInsightsByPriority 
  } = useAIInsights();

  // Filter insights based on selected priority
  const filteredInsights = getInsightsByPriority(selectedPriority);

  // Update priority filter counts
  const priorityFilters = [
    { value: 'all' as const, label: 'Alles', count: insights.length },
    { value: 'high' as const, label: 'Hoog', count: insights.filter(i => i.priority === 'high').length },
    { value: 'medium' as const, label: 'Medium', count: insights.filter(i => i.priority === 'medium').length },
    { value: 'low' as const, label: 'Laag', count: insights.filter(i => i.priority === 'low').length },
  ];

  const handleInsightAction = (insight: any) => {
    // Mark as read when clicked
    markAsRead(insight.id);
    console.log('Navigate to:', insight.actionUrl);
    // TODO: Implement navigation
  };

  if (loading) {
    return (
      <div className={cn(
        'fixed bottom-4 right-4 z-50',
        className
      )}>
        <Button
          className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 shadow-lg"
          disabled
        >
          <Star size={24} className="text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className={cn(
      'fixed bottom-4 right-4 z-50 transition-all duration-300',
      isExpanded ? 'w-96' : 'w-16',
      className
    )}>
      {/* Collapsed State */}
      {!isExpanded && (
        <Button
          onClick={() => setIsExpanded(true)}
          className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 shadow-lg relative"
        >
          <Star size={24} className="text-white" />
          {stats.unreadInsights > 0 && (
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">
                {stats.unreadInsights > 9 ? '9+' : stats.unreadInsights}
              </span>
            </div>
          )}
        </Button>
      )}

      {/* Expanded State */}
      {isExpanded && (
        <Card className="bg-glass-dark backdrop-blur-sm border border-white/20 shadow-xl max-h-[600px] overflow-hidden">
          {/* Header */}
          <CardHeader title="" className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Star size={16} className="text-white" />
                </div>
                <div>
                  <h3 className="text-white font-semibold text-lg">AI Insights</h3>
                  <p className="text-white/60 text-sm">Slimme meldingen & kansen</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(false)}
                className="text-white/60 hover:text-white"
              >
                <ChevronDown size={16} />
              </Button>
            </div>
          </CardHeader>

          {/* Error Message */}
          {error && (
            <div className="px-4 pb-3">
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-2">
                <p className="text-red-300 text-xs">{error}</p>
              </div>
            </div>
          )}

          {/* Priority Filters */}
          <div className="px-4 pb-3">
            <div className="flex gap-2 overflow-x-auto">
              {priorityFilters.map((filter) => (
                <Button
                  key={filter.value}
                  variant={selectedPriority === filter.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedPriority(filter.value)}
                  className={cn(
                    'whitespace-nowrap',
                    selectedPriority === filter.value 
                      ? 'bg-primary-500 hover:bg-primary-600' 
                      : 'bg-white/5 hover:bg-white/10'
                  )}
                >
                  {filter.label} ({filter.count})
                </Button>
              ))}
            </div>
          </div>

          {/* Insights List */}
          <div className="px-4 pb-4 max-h-[300px] overflow-y-auto">
            {filteredInsights.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-white/40 text-sm">Geen insights gevonden</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredInsights.map((insight) => (
                  <div
                    key={insight.id}
                    className={cn(
                      'p-3 rounded-lg border bg-white/5 hover:bg-white/10 transition-colors cursor-pointer',
                      insight.borderColor,
                      !insight.isRead && 'border-l-4 border-l-primary-500'
                    )}
                    onClick={() => handleInsightAction(insight)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center flex-shrink-0">
                        {iconMap[insight.icon] || <Eye size={16} />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-white font-medium text-sm">
                            {insight.title}
                          </h4>
                          {insight.isRead && (
                            <CheckCircle size={12} className="text-green-400" />
                          )}
                        </div>
                        <p className="text-white/60 text-xs mb-2 leading-relaxed">
                          {insight.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-white/40 text-xs flex items-center gap-1">
                            <Clock size={12} />
                            {insight.timestamp}
                          </span>
                          <span className="text-primary-400 text-xs flex items-center gap-1 hover:text-primary-300">
                            {insight.actionText}
                            <ArrowRight size={12} />
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Summary Stats */}
          <div className="px-4 pb-3 border-t border-white/10 pt-3">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-white font-bold text-xl">{stats.newLeads}</div>
                <div className="text-white/60 text-xs">Nieuwe leads</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-bold text-xl">€{stats.potentialRevenue.toLocaleString()}</div>
                <div className="text-white/60 text-xs">Potentiële omzet</div>
              </div>
            </div>
          </div>

          {/* AI Status */}
          <div className="px-4 pb-4">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-300 text-xs font-medium">AI Actief</span>
              <span className="text-white/40 text-xs">•</span>
              <span className="text-white/40 text-xs">Real-time monitoring</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}; 