import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  Edit, 
  Trash2,
  Sparkles,
  FileText,
  Euro,
  Calendar,
  User,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { invoiceApi } from '@/api/invoices';
import { Invoice } from '@/types/invoice';
import { NewAIInvoiceModal } from './NewAIInvoiceModal';
import { cn } from '@/utils/cn';

interface InvoiceListProps {
  onInvoiceClick?: (invoice: Invoice) => void;
  onEditInvoice?: (invoice: Invoice) => void;
  onDeleteInvoice?: (invoiceId: string) => void;
}

export const InvoiceList: React.FC<InvoiceListProps> = ({
  onInvoiceClick,
  onEditInvoice,
  onDeleteInvoice
}) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showAIModal, setShowAIModal] = useState(false);

  // Load invoices
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await invoiceApi.getInvoices();
      setInvoices(data);
    } catch (error) {
      console.error('Error loading invoices:', error);
      setError('Fout bij het laden van facturen');
    } finally {
      setLoading(false);
    }
  };

  const handleInvoiceCreated = (newInvoice: Invoice) => {
    setInvoices(prev => [newInvoice, ...prev]);
  };

  const handleDeleteInvoice = async (invoiceId: string) => {
    if (window.confirm('Weet je zeker dat je deze factuur wilt verwijderen?')) {
      try {
        const success = await invoiceApi.deleteInvoice(invoiceId);
        if (success) {
          setInvoices(prev => prev.filter(inv => inv.id !== invoiceId));
        }
      } catch (error) {
        console.error('Error deleting invoice:', error);
      }
    }
  };

  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'paid':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'sent':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'overdue':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'draft':
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      case 'cancelled':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: Invoice['status']) => {
    switch (status) {
      case 'paid':
        return <CheckCircle size={14} />;
      case 'sent':
        return <Clock size={14} />;
      case 'overdue':
        return <AlertTriangle size={14} />;
      case 'draft':
        return <FileText size={14} />;
      case 'cancelled':
        return <XCircle size={14} />;
      default:
        return <FileText size={14} />;
    }
  };

  const getStatusText = (status: Invoice['status']) => {
    switch (status) {
      case 'paid':
        return 'Betaald';
      case 'sent':
        return 'Verzonden';
      case 'overdue':
        return 'Achterstallig';
      case 'draft':
        return 'Concept';
      case 'cancelled':
        return 'Geannuleerd';
      default:
        return status;
    }
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.projectName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || invoice.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-300 mb-4">{error}</p>
        <Button onClick={loadInvoices} variant="outline">
          Opnieuw proberen
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Facturen</h1>
          <p className="text-gray-300">Beheer uw facturen en betalingen</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => setShowAIModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Sparkles size={16} className="mr-2" />
            + AI Factuur
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Zoeken in facturen..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Zoeken in facturen"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Filter op status"
          >
            <option value="all">Alle statussen</option>
            <option value="draft">Concept</option>
            <option value="sent">Verzonden</option>
            <option value="paid">Betaald</option>
            <option value="overdue">Achterstallig</option>
            <option value="cancelled">Geannuleerd</option>
          </select>
        </div>
        
        <div className="text-sm text-gray-400">
          {filteredInvoices.length} van {invoices.length} facturen
        </div>
      </div>

      {/* Invoices List */}
      <div className="space-y-3">
        {filteredInvoices.length === 0 ? (
          <div className="text-center py-12 text-gray-400">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>Geen facturen gevonden</p>
            <p className="text-sm">Maak je eerste factuur aan met de AI Factuur Wizard</p>
          </div>
        ) : (
          filteredInvoices.map((invoice) => (
            <div
              key={invoice.id}
              className="flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:bg-slate-800/50 transition-colors cursor-pointer"
              onClick={() => onInvoiceClick?.(invoice)}
            >
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <FileText className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-medium">{invoice.number}</h3>
                  <p className="text-gray-400 text-sm">{invoice.customerName}</p>
                  {invoice.projectName && (
                    <p className="text-gray-500 text-xs">{invoice.projectName}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-white font-medium">€{invoice.total.toLocaleString()}</p>
                  <p className="text-gray-400 text-sm">
                    <Calendar className="inline-block mr-1 h-3 w-3" />
                    {new Date(invoice.dueDate).toLocaleDateString('nl-NL')}
                  </p>
                </div>
                
                <span className={cn(
                  'px-3 py-1 rounded-full text-xs font-medium border',
                  getStatusColor(invoice.status)
                )}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(invoice.status)}
                    {getStatusText(invoice.status)}
                  </div>
                </span>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onInvoiceClick?.(invoice);
                    }}
                    title="Bekijk factuur"
                    aria-label="Bekijk factuur"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Download PDF
                      window.open(invoice.pdfUrl, '_blank');
                    }}
                    title="Download factuur"
                    aria-label="Download factuur"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditInvoice?.(invoice);
                    }}
                    title="Bewerk factuur"
                    aria-label="Bewerk factuur"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteInvoice(invoice.id);
                    }}
                    title="Verwijder factuur"
                    aria-label="Verwijder factuur"
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* AI Invoice Modal */}
      <NewAIInvoiceModal
        isOpen={showAIModal}
        onClose={() => setShowAIModal(false)}
        onInvoiceCreated={handleInvoiceCreated}
      />
    </div>
  );
}; 