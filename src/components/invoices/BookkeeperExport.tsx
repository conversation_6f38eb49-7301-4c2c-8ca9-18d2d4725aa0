import React, { useState } from 'react';
import { Download, CheckCircle } from 'lucide-react';
import { ExportConfig } from '../../types/invoice';

interface BookkeeperExportProps {
  onExport: (config: ExportConfig) => Promise<void>;
  invoiceCount: number;
  totalAmount: number;
  vatAmount: number;
}

const BookkeeperExport: React.FC<BookkeeperExportProps> = ({
  onExport,
  invoiceCount,
  totalAmount,
  vatAmount
}) => {
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    format: 'excel',
    dateRange: { start: '', end: '' },
    includeTypes: ['sales', 'purchase'],
    onlyPaid: false,
    groupByMonth: false
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportSuccess, setExportSuccess] = useState(false);

  const exportFormats = [
    {
      id: 'excel',
      name: 'Excel (.xlsx)',
      description: 'Standaard Excel format voor meeste boekhouders',
      icon: '📊',
      recommended: true
    },
    {
      id: 'csv',
      name: 'CSV',
      description: 'Komma-gescheiden bestand voor import in boekhoudpakketten',
      icon: '📄'
    },
    {
      id: 'exact',
      name: 'Exact Online',
      description: 'Direct import format voor Exact Online',
      icon: '💼'
    },
    {
      id: 'twinfield',
      name: 'Twinfield',
      description: 'Import format voor Twinfield boekhoudpakket',
      icon: '📋'
    }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    setExportSuccess(false);
    
    try {
      await onExport(exportConfig);
      setExportSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setExportSuccess(false), 3000);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const updateConfig = (key: keyof ExportConfig, value: any) => {
    setExportConfig(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="bg-slate-800 rounded-xl p-6">
      <div className="flex items-center gap-3 mb-6">
        <Download className="w-6 h-6 text-green-500" />
        <h3 className="text-xl font-bold text-white">
          Export naar Boekhouder
        </h3>
      </div>

      {/* Export Format Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-slate-300 mb-3">
          Export Format
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {exportFormats.map((format) => (
            <button
              key={format.id}
              onClick={() => updateConfig('format', format.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                exportConfig.format === format.id
                  ? 'border-green-500 bg-green-500/10'
                  : 'border-slate-600 bg-slate-700 hover:border-slate-500'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className="text-2xl">{format.icon}</span>
                <div className="text-left">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-white">{format.name}</span>
                    {format.recommended && (
                      <span className="text-xs bg-green-500 text-white px-2 py-1 rounded-full">
                        Aanbevolen
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-slate-400">{format.description}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Start Datum
          </label>
          <input
            type="date"
            value={exportConfig.dateRange.start}
            onChange={(e) => updateConfig('dateRange', { 
              ...exportConfig.dateRange, 
              start: e.target.value 
            })}
            className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
            aria-label="Start datum voor export"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Eind Datum
          </label>
          <input
            type="date"
            value={exportConfig.dateRange.end}
            onChange={(e) => updateConfig('dateRange', { 
              ...exportConfig.dateRange, 
              end: e.target.value 
            })}
            className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
            aria-label="Eind datum voor export"
          />
        </div>
      </div>

      {/* Export Options */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-3">
            Factuur Types
          </label>
          <div className="flex gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportConfig.includeTypes.includes('sales')}
                onChange={(e) => {
                  const newTypes = e.target.checked 
                    ? [...exportConfig.includeTypes, 'sales']
                    : exportConfig.includeTypes.filter(t => t !== 'sales');
                  updateConfig('includeTypes', newTypes);
                }}
                className="rounded border-slate-600 bg-slate-700 text-green-500"
              />
              <span className="text-sm text-slate-300">Verkoop Facturen</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportConfig.includeTypes.includes('purchase')}
                onChange={(e) => {
                  const newTypes = e.target.checked 
                    ? [...exportConfig.includeTypes, 'purchase']
                    : exportConfig.includeTypes.filter(t => t !== 'purchase');
                  updateConfig('includeTypes', newTypes);
                }}
                className="rounded border-slate-600 bg-slate-700 text-green-500"
              />
              <span className="text-sm text-slate-300">Aankoop Facturen</span>
            </label>
          </div>
        </div>

        <div className="flex gap-4">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={exportConfig.onlyPaid}
              onChange={(e) => updateConfig('onlyPaid', e.target.checked)}
              className="rounded border-slate-600 bg-slate-700 text-green-500"
            />
            <span className="text-sm text-slate-300">Alleen betaalde facturen</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={exportConfig.groupByMonth}
              onChange={(e) => updateConfig('groupByMonth', e.target.checked)}
              className="rounded border-slate-600 bg-slate-700 text-green-500"
            />
            <span className="text-sm text-slate-300">Groeperen per maand</span>
          </label>
        </div>
      </div>

      {/* Export Summary */}
      <div className="bg-slate-700 rounded-lg p-4 mb-6">
        <h4 className="font-medium text-white mb-3">Export Samenvatting</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-slate-400">Aantal Facturen</p>
            <p className="text-white font-medium">{invoiceCount}</p>
          </div>
          <div>
            <p className="text-slate-400">Totaal Bedrag</p>
            <p className="text-white font-medium">€{totalAmount.toLocaleString('nl-NL')}</p>
          </div>
          <div>
            <p className="text-slate-400">BTW Bedrag</p>
            <p className="text-white font-medium">€{vatAmount.toLocaleString('nl-NL')}</p>
          </div>
          <div>
            <p className="text-slate-400">Format</p>
            <p className="text-white font-medium">
              {exportFormats.find(f => f.id === exportConfig.format)?.name}
            </p>
          </div>
        </div>
      </div>

      {/* Export Button */}
      <button
        onClick={handleExport}
        disabled={isExporting || exportConfig.includeTypes.length === 0}
        className={`w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-colors ${
          isExporting || exportConfig.includeTypes.length === 0
            ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
            : 'bg-green-600 hover:bg-green-700 text-white'
        }`}
      >
        {isExporting ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            Exporteren...
          </>
        ) : exportSuccess ? (
          <>
            <CheckCircle className="w-5 h-5" />
            Export Succesvol!
          </>
        ) : (
          <>
            <Download className="w-5 h-5" />
            Exporteren voor Boekhouder
          </>
        )}
      </button>

      {/* Export Tips */}
      <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h5 className="font-medium text-blue-400 mb-2">💡 Export Tips</h5>
        <ul className="text-sm text-slate-300 space-y-1">
          <li>• Excel format werkt met de meeste boekhoudpakketten</li>
          <li>• CSV is ideaal voor grote datasets</li>
          <li>• Exact Online en Twinfield formats zijn direct importeerbaar</li>
          <li>• Export wordt automatisch gelogd voor audit trail</li>
        </ul>
      </div>
    </div>
  );
};

export default BookkeeperExport; 