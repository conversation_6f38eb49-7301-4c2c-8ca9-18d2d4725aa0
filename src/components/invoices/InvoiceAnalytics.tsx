import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  AlertTriangle,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { InvoiceAnalytics as InvoiceAnalyticsType } from '../../types/invoice';

interface InvoiceAnalyticsProps {
  analytics: InvoiceAnalyticsType;
  period: 'week' | 'month' | 'quarter' | 'year';
  onPeriodChange: (period: 'week' | 'month' | 'quarter' | 'year') => void;
}

const InvoiceAnalytics: React.FC<InvoiceAnalyticsProps> = ({
  analytics,
  period,
  onPeriodChange
}) => {
  const periods = [
    { id: 'week', label: 'Week', icon: Calendar },
    { id: 'month', label: 'Maand', icon: Calendar },
    { id: 'quarter', label: 'Kwartaal', icon: BarChart3 },
    { id: 'year', label: 'Jaar', icon: TrendingUp }
  ];

  const formatCurrency = (amount: number) => {
    return `€${amount.toLocaleString('nl-NL')}`;
  };

  const getCashFlowColor = (net: number) => {
    return net >= 0 ? 'text-green-500' : 'text-red-500';
  };

  const getCashFlowIcon = (net: number) => {
    return net >= 0 ? TrendingUp : TrendingDown;
  };

  return (
    <div className="bg-slate-800 rounded-xl p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">Financiële Analytics</h3>
          <p className="text-slate-400 text-sm">
            Inzicht in je cash flow en financiële prestaties
          </p>
        </div>
        
        {/* Period Selector */}
        <div className="flex bg-slate-700 rounded-lg p-1">
          {periods.map((p) => {
            const Icon = p.icon;
            return (
              <button
                key={p.id}
                onClick={() => onPeriodChange(p.id as any)}
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors ${
                  period === p.id
                    ? 'bg-blue-600 text-white'
                    : 'text-slate-400 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                {p.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-slate-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Totaal Uitstaand</p>
              <p className="text-2xl font-bold text-white">
                {formatCurrency(analytics.totalOutstanding)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Deze {period}</p>
              <p className="text-2xl font-bold text-white">
                {formatCurrency(analytics.monthlyTotal)}
              </p>
            </div>
            <Calendar className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Te Laat</p>
              <p className="text-2xl font-bold text-red-500">
                {formatCurrency(analytics.overdueAmount)}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Gem. Betaaltijd</p>
              <p className="text-2xl font-bold text-white">
                {analytics.averagePaymentTime} dagen
              </p>
            </div>
            <Activity className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Cash Flow Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-slate-700 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4">Cash Flow Projectie</h4>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-300">Inkomende Betalingen</span>
              <span className="text-green-500 font-medium">
                {formatCurrency(analytics.cashFlowProjection.incoming)}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-slate-300">Uitgaande Betalingen</span>
              <span className="text-red-500 font-medium">
                {formatCurrency(analytics.cashFlowProjection.outgoing)}
              </span>
            </div>
            
            <div className="border-t border-slate-600 pt-4">
              <div className="flex justify-between items-center">
                <span className="text-slate-300 font-medium">Netto Cash Flow</span>
                <div className="flex items-center gap-2">
                  {React.createElement(getCashFlowIcon(analytics.cashFlowProjection.net), {
                    className: `w-4 h-4 ${getCashFlowColor(analytics.cashFlowProjection.net)}`
                  })}
                  <span className={`font-bold ${getCashFlowColor(analytics.cashFlowProjection.net)}`}>
                    {formatCurrency(analytics.cashFlowProjection.net)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4">Winst Analyse</h4>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-300">Bruto Winst</span>
              <span className="text-green-500 font-medium">
                {formatCurrency(analytics.profitAnalysis.grossProfit)}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-slate-300">Winstmarge</span>
              <span className="text-blue-500 font-medium">
                {analytics.profitAnalysis.profitMargin.toFixed(1)}%
              </span>
            </div>
            
            <div className="border-t border-slate-600 pt-4">
              <h5 className="text-sm font-medium text-slate-300 mb-3">Uitgaven per Categorie</h5>
              <div className="space-y-2">
                {Object.entries(analytics.profitAnalysis.expensesByCategory)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 3)
                  .map(([category, amount]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-slate-400 text-sm">{category}</span>
                      <span className="text-slate-300 text-sm">
                        {formatCurrency(amount)}
                      </span>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-slate-700 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="w-5 h-5 text-blue-500" />
            <h4 className="text-lg font-semibold text-white">Factuur Volume</h4>
          </div>
          <div className="h-48 flex items-center justify-center">
            <p className="text-slate-400">Chart placeholder - Factuur volume over tijd</p>
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <PieChart className="w-5 h-5 text-green-500" />
            <h4 className="text-lg font-semibold text-white">Uitgaven Breakdown</h4>
          </div>
          <div className="h-48 flex items-center justify-center">
            <p className="text-slate-400">Chart placeholder - Uitgaven per categorie</p>
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <h5 className="font-medium text-blue-400 mb-3">💡 Financiële Inzichten</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
          <div>
            <p className="font-medium text-white mb-1">Cash Flow Positief</p>
            <p>Je hebt een positieve cash flow van {formatCurrency(analytics.cashFlowProjection.net)} deze {period}.</p>
          </div>
          <div>
            <p className="font-medium text-white mb-1">Betaaltijd Optimalisatie</p>
            <p>Gemiddelde betaaltijd van {analytics.averagePaymentTime} dagen is {analytics.averagePaymentTime > 30 ? 'boven' : 'onder'} de norm van 30 dagen.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceAnalytics; 