import React, { useState, useMemo } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Building,
  MapPin,
  Calendar
} from 'lucide-react';
import { useCustomers } from '@/hooks/useCustomers';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@/components/ui/Button';
import { Table, Column, SortConfig } from '@/components/ui/Table';
import { Modal } from '@/components/ui/Modal';
import { FormInput, FormSelect, FormFieldGroup } from '@/components/ui/Form';
import { Customer } from '@/stores/useAppStore';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';

// Customer status options
const statusOptions = [
  { value: 'active', label: 'Actief' },
  { value: 'inactive', label: 'Inactief' },
  { value: 'prospect', label: 'Prospect' },
  { value: 'lead', label: 'Lead' },
];

// Status badge component
const StatusBadge: React.FC<{ status: Customer['status'] }> = ({ status }) => {
  const statusConfig = {
    active: { color: 'bg-green-500', text: 'Actief' },
    inactive: { color: 'bg-gray-500', text: 'Inactief' },
    prospect: { color: 'bg-blue-500', text: 'Prospect' },
    lead: { color: 'bg-yellow-500', text: 'Lead' },
  };

  const config = statusConfig[status];

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${config.color}`}>
      {config.text}
    </span>
  );
};

export const CustomerList: React.FC = () => {
  const { customers, deleteCustomerById } = useCustomers();
  const { showToast } = useToast();
  
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [sortConfig, setSortConfig] = useState<SortConfig | undefined>();
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);

  // Filtered and sorted customers
  const filteredCustomers = useMemo(() => {
    let filtered = customers;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(customer => 
        customer.name.toLowerCase().includes(query) ||
        customer.email.toLowerCase().includes(query) ||
        customer.company?.toLowerCase().includes(query) ||
        customer.address.toLowerCase().includes(query)
      );
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(customer => customer.status === statusFilter);
    }

    // Sorting
    if (sortConfig) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof Customer];
        const bValue = b[sortConfig.key as keyof Customer];
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        if (aValue instanceof Date && bValue instanceof Date) {
          return sortConfig.direction === 'asc'
            ? aValue.getTime() - bValue.getTime()
            : bValue.getTime() - aValue.getTime();
        }
        
        return 0;
      });
    }

    return filtered;
  }, [customers, searchQuery, statusFilter, sortConfig]);

  // Table columns
  const columns: Column<Customer>[] = [
    {
      key: 'name',
      header: 'Klant',
      accessor: (customer) => (
        <div>
          <div className="font-medium text-white">{customer.name}</div>
          {customer.company && (
            <div className="text-sm text-gray-400">{customer.company}</div>
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'email',
      header: 'Contact',
      accessor: (customer) => (
        <div className="space-y-1">
          <div className="flex items-center text-sm">
            <Mail className="h-3 w-3 mr-1 text-gray-400" />
            <span className="text-gray-300">{customer.email}</span>
          </div>
          <div className="flex items-center text-sm">
            <Phone className="h-3 w-3 mr-1 text-gray-400" />
            <span className="text-gray-300">{customer.phone}</span>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'address',
      header: 'Adres',
      accessor: (customer) => (
        <div className="flex items-start text-sm">
          <MapPin className="h-3 w-3 mr-1 text-gray-400 mt-0.5 flex-shrink-0" />
          <span className="text-gray-300">{customer.address}</span>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'status',
      header: 'Status',
      accessor: (customer) => <StatusBadge status={customer.status} />,
      sortable: true,
    },
    {
      key: 'createdAt',
      header: 'Aangemaakt',
      accessor: (customer) => (
        <div className="flex items-center text-sm">
          <Calendar className="h-3 w-3 mr-1 text-gray-400" />
          <span className="text-gray-300">
            {format(customer.createdAt, 'dd MMM yyyy', { locale: nl })}
          </span>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Acties',
      accessor: (customer) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleViewCustomer(customer)}
            className="p-1 text-gray-400 hover:text-white transition-colors"
            title="Bekijken"
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEditCustomer(customer)}
            className="p-1 text-gray-400 hover:text-white transition-colors"
            title="Bewerken"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDeleteCustomer(customer)}
            className="p-1 text-gray-400 hover:text-red-400 transition-colors"
            title="Verwijderen"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  // Handlers
  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        return {
          key,
          direction: current.direction === 'asc' ? 'desc' : 'asc',
        };
      }
      return { key, direction: 'asc' };
    });
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerModal(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    // TODO: Navigate to edit page
    showToast({
      type: 'info',
      title: 'Bewerken',
      message: `Klant ${customer.name} wordt bewerkt.`,
    });
  };

  const handleDeleteCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedCustomer) {return;}

    try {
      await deleteCustomerById(selectedCustomer.id);
      setShowDeleteModal(false);
      setSelectedCustomer(null);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    showToast({
      type: 'info',
      title: 'Export',
      message: 'Klanten worden geëxporteerd...',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Klanten</h1>
          <p className="text-gray-300">Beheer al uw klanten en contacten</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="glass" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exporteren
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Nieuwe Klant
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormInput
            placeholder="Zoeken in klanten..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
          <FormSelect
            placeholder="Status filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'Alle statussen' },
              ...statusOptions,
            ]}
          />
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-400">
              {filteredCustomers.length} van {customers.length} klanten
            </span>
          </div>
        </div>
      </div>

      {/* Table */}
      <Table
        data={filteredCustomers}
        columns={columns}
        sortConfig={sortConfig}
        onSort={handleSort}
        emptyMessage="Geen klanten gevonden"
      />

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Klant Verwijderen"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Weet u zeker dat u klant <strong>{selectedCustomer?.name}</strong> wilt verwijderen?
            Deze actie kan niet ongedaan worden gemaakt.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowDeleteModal(false)}
            >
              Annuleren
            </Button>
            <Button
              variant="danger"
              onClick={confirmDelete}
            >
              Verwijderen
            </Button>
          </div>
        </div>
      </Modal>

      {/* Customer Detail Modal */}
      <Modal
        isOpen={showCustomerModal}
        onClose={() => setShowCustomerModal(false)}
        title={`Klant: ${selectedCustomer?.name}`}
        size="lg"
      >
        {selectedCustomer && (
          <div className="space-y-6">
            {/* Customer Header */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  Bedrijfsinformatie
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-400">Naam</p>
                    <p className="text-white font-medium">{selectedCustomer.name}</p>
                  </div>
                  {selectedCustomer.company && (
                    <div>
                      <p className="text-sm text-gray-400">Bedrijf</p>
                      <p className="text-white font-medium">{selectedCustomer.company}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm text-gray-400">Status</p>
                    <StatusBadge status={selectedCustomer.status} />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Contact Informatie
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-400">Email</p>
                    <p className="text-white font-medium">{selectedCustomer.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Telefoon</p>
                    <p className="text-white font-medium">{selectedCustomer.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Adres</p>
                    <p className="text-white font-medium">{selectedCustomer.address}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Activity */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Activiteit
              </h3>
              <div className="bg-glass-dark rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-400">Aangemaakt op</p>
                    <p className="text-white font-medium">
                      {format(selectedCustomer.createdAt, 'dd MMMM yyyy', { locale: nl })}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Laatste activiteit</p>
                    <p className="text-white font-medium">Vandaag</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-white/20">
              <Button variant="ghost">
                <Phone className="h-4 w-4 mr-2" />
                Bellen
              </Button>
              <Button variant="glass">
                <Mail className="h-4 w-4 mr-2" />
                Email Sturen
              </Button>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                Bewerken
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}; 