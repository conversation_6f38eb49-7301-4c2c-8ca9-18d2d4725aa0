import React, { useState, useEffect } from 'react';
import { 
  Save, 
  X, 
  User,
  Building,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';
import { useCustomers } from '@/hooks/useCustomers';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';
import { 
  FormInput, 
  FormTextarea, 
  FormSelect, 
  FormFieldGroup 
} from '@/components/ui/Form';
import { Customer } from '@/stores/useAppStore';

interface CustomerFormProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer | null;
  mode: 'create' | 'edit';
}

const statusOptions = [
  { value: 'active', label: 'Actief' },
  { value: 'inactive', label: 'Inactief' },
  { value: 'prospect', label: 'Prospect' },
  { value: 'lead', label: 'Lead' },
];

export const CustomerForm: React.FC<CustomerFormProps> = ({
  isOpen,
  onClose,
  customer,
  mode,
}) => {
  const { createCustomer, updateCustomerById } = useCustomers();
  const { showToast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    address: '',
    status: 'active' as Customer['status'],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form with customer data
  useEffect(() => {
    if (customer && mode === 'edit') {
      setFormData({
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        company: customer.company || '',
        address: customer.address,
        status: customer.status,
      });
    } else {
      // Reset form for new customer
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        address: '',
        status: 'active',
      });
    }
    setErrors({});
  }, [customer, mode]);

  // Validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Naam is verplicht';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is verplicht';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Voer een geldig email adres in';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefoonnummer is verplicht';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Adres is verplicht';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handlers
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {return;}

    try {
      const customerData = {
        ...formData,
        company: formData.company || undefined, // Don't save empty company
      };

      if (mode === 'create') {
        await createCustomer(customerData);
        showToast({
          type: 'success',
          title: 'Klant Aangemaakt',
          message: 'De klant is succesvol aangemaakt.',
        });
      } else if (customer) {
        await updateCustomerById(customer.id, customerData);
        showToast({
          type: 'success',
          title: 'Klant Bijgewerkt',
          message: 'De klant is succesvol bijgewerkt.',
        });
      }

      onClose();
    } catch (error) {
      // Error is handled by the hook
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Nieuwe Klant' : 'Klant Bewerken'}
      size="lg"
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <FormFieldGroup>
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Basis Informatie
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Naam *"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              placeholder="Volledige naam"
            />
            
            <FormSelect
              label="Status"
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              options={statusOptions}
            />
          </div>
        </FormFieldGroup>

        {/* Company Information */}
        <FormFieldGroup>
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Bedrijfsinformatie
          </h3>
          
          <FormInput
            label="Bedrijfsnaam"
            value={formData.company}
            onChange={(e) => handleInputChange('company', e.target.value)}
            placeholder="Optioneel - bedrijfsnaam"
          />
        </FormFieldGroup>

        {/* Contact Information */}
        <FormFieldGroup>
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Contact Informatie
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Email *"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={errors.email}
              placeholder="<EMAIL>"
            />
            
            <FormInput
              label="Telefoon *"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={errors.phone}
              placeholder="+31 6 12345678"
            />
          </div>
        </FormFieldGroup>

        {/* Address Information */}
        <FormFieldGroup>
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Adres Informatie
          </h3>
          
          <FormTextarea
            label="Adres *"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            error={errors.address}
            placeholder="Straatnaam, huisnummer, postcode en plaats"
            rows={3}
          />
        </FormFieldGroup>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-white/20">
          <Button variant="ghost" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            Annuleren
          </Button>
          
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Opslaan
          </Button>
        </div>
      </div>
    </Modal>
  );
}; 