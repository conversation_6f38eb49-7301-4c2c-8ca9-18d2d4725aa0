import React, { useState, useEffect } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { QuickActionBar } from './QuickActionBar';
import { Toaster } from 'react-hot-toast';
import { useAppStore } from '@/stores/useAppStore';
import { cn } from '@/utils/cn';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { AIInsightsWidget } from '../ai/AIInsightsWidget';
import { QuickActions } from '../ui/QuickActions';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { sidebarCollapsed } = useAppStore();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1024);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="flex h-screen">
        <Sidebar />
        <div className={cn(
          'flex flex-col overflow-hidden transition-all duration-500 ease-out w-full',
          'will-change-transform touch-manipulation',
          isMobile ? 'ml-0' : (sidebarCollapsed ? 'ml-16' : 'ml-64')
        )}>
          <Header />
          <QuickActionBar />
          <main className="flex-1 overflow-y-auto w-full">
            <div className="w-full px-4 py-6">
              {children}
            </div>
          </main>
        </div>
      </div>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 5000,
          style: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: 'white',
          },
        }}
      />

      {/* AI Insights Widget - Available on all pages */}
      <AIInsightsWidget />
      <QuickActions />
    </div>
  );
};
