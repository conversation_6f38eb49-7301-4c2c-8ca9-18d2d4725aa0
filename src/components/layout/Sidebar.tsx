import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  FileText, 
  Users, 
  Building, 
  Settings, 
  BarChart3,
  MessageSquare,
  Menu,
  X,
  Mail,
  Receipt,
  Bot
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { ChevronIcon } from '@/components/ui';

import { useAppStore } from '@/stores/useAppStore';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Offertes', href: '/quotes', icon: FileText },
  { name: 'Klanten', href: '/customers', icon: Users },
  { name: 'Projecten', href: '/projects', icon: Building },
  { name: 'Facturen', href: '/invoices', icon: Receipt },
  { name: 'Email', href: '/email', icon: Mail },
  { name: 'Innovars AI', href: '/innovars-ai', icon: MessageSquare },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Instellingen', href: '/settings', icon: Settings },
  {
    name: 'AI Smart Quote',
    href: '/ai-smart-quote',
    icon: Bot,
    badge: 'AI'
  }
];

export const Sidebar: React.FC = () => {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const location = useLocation();
  const { sidebarCollapsed, setSidebarCollapsed } = useAppStore();

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileOpen(!isMobileOpen)}
          className="p-2 rounded-md bg-glass-light backdrop-blur-sm border border-white/20 text-white hover:bg-glass-dark transition-colors"
        >
          {isMobileOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          'group fixed inset-y-0 left-0 z-40 bg-glass-light backdrop-blur-md border-r border-white/20 transform transition-all duration-300 ease-in-out lg:translate-x-0',
          sidebarCollapsed ? 'w-16 hover:w-60' : 'w-60',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo and Toggle */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-white/20">
            <h1 className={cn(
              'text-xl font-bold text-white whitespace-nowrap transition-opacity duration-200',
              sidebarCollapsed ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
            )}>Quote.AI+CRM</h1>
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className='p-1 rounded-md text-gray-300 hover:text-white hover:bg-glass-dark transition-colors'
              title={sidebarCollapsed ? 'Uitklappen' : 'Inklappen'}
            >
              <ChevronIcon isCollapsed={sidebarCollapsed} />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.href === '/innovars-ai' && location.pathname === '/rita');
              return (
                <div key={item.name} className="relative">
                  <Link
                    to={item.href}
                    className={cn(
                      'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors w-full group/item',
                      isActive
                        ? 'bg-primary-600 text-white shadow-glow'
                        : 'text-gray-300 hover:text-white hover:bg-glass-dark',
                      sidebarCollapsed && 'justify-center'
                    )}
                    onClick={() => isMobileOpen && setIsMobileOpen(false)}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    <span className={cn(
                      'ml-3 truncate transition-opacity',
                      sidebarCollapsed ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
                    )}>{item.name}</span>
                    {item.badge && (
                      <span className={cn(
                        'ml-auto bg-blue-500 text-white text-xs font-semibold px-2 py-0.5 rounded-full transition-opacity',
                        sidebarCollapsed ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
                      )}>
                        {item.badge}
                      </span>
                    )}
                    {/* Custom Tooltip */}
                    <span className={cn(
                      'absolute left-full top-1/2 -translate-y-1/2 ml-4 px-2 py-1 rounded-md bg-gray-900 text-white text-sm whitespace-nowrap',
                      'transition-all scale-95 opacity-0 pointer-events-none',
                      'group-hover/item:scale-100 group-hover/item:opacity-100',
                      !sidebarCollapsed && 'hidden' // Hide tooltip completely when sidebar is expanded
                    )}>
                      {item.name}
                    </span>
                  </Link>
                </div>
              );
            })}
          </nav>

          {/* User info */}
          <div className="p-4 border-t border-white/20">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                <span className="text-sm font-medium text-white">U</span>
              </div>
              <div className={cn(
                'ml-3 truncate transition-opacity',
                sidebarCollapsed ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
              )}>
                <p className="text-sm font-medium text-white">Gebruiker</p>
                <p className="text-xs text-gray-400">Premium Plan</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}
    </>
  );
};
