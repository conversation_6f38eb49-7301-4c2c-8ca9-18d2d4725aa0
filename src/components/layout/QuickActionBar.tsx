import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, FileText, Bot } from 'lucide-react';

interface QuickActionBarProps {
  className?: string;
}

interface ActionBtnProps {
  color: string;
  icon: React.ReactNode;
  label: string;
  badge?: string;
  onClick: () => void;
}

function ActionBtn({ color, icon, label, badge, onClick }: ActionBtnProps) {
  const colorClasses = {
    blue: 'bg-blue-500 hover:bg-blue-400',
    indigo: 'bg-indigo-500 hover:bg-indigo-400',
    purple: 'bg-purple-500 hover:bg-purple-400',
    green: 'bg-green-500 hover:bg-green-400',
    red: 'bg-red-500 hover:bg-red-400',
    yellow: 'bg-yellow-500 hover:bg-yellow-400',
  };

  return (
    <button
      onClick={onClick}
      className={`relative flex items-center gap-2 px-4 py-2 text-white
                  ${colorClasses[color as keyof typeof colorClasses] || colorClasses.blue}
                  rounded-lg transition-transform duration-150 hover:scale-105`}
    >
      {icon}
      <span className="font-medium">{label}</span>
      {badge && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-[10px] w-5 h-5
                         flex items-center justify-center rounded-full text-white font-bold">
          {badge}
        </span>
      )}
    </button>
  );
}

export const QuickActionBar: React.FC<QuickActionBarProps> = ({ className }) => {
  const navigate = useNavigate();

  const actions = [
    {
      id: 'add-property',
      label: 'Add Property',
      icon: <Plus size={16} />,
      color: 'blue' as const,
      onClick: () => navigate('/properties?action=new')
    },
    {
      id: 'create-quote',
      label: 'Create Quote',
      icon: <FileText size={16} />,
      color: 'indigo' as const,
      badge: '3',
      onClick: () => navigate('/quotes?action=new')
    },
    {
      id: 'ai-assistant',
      label: 'AI Assistant',
      icon: <Bot size={16} />,
      color: 'purple' as const,
      onClick: () => navigate('/innovars-ai')
    }
  ];

  return (
    <div className={`w-full px-4 py-2 flex flex-wrap gap-3 items-center justify-center
                    bg-[rgba(15,23,42,0.55)] backdrop-blur-md 
                    bg-gradient-to-r from-indigo-600/20 via-sky-600/20 to-purple-600/20
                    border border-white/10 shadow-[0_2px_8px_-2px_rgba(0,0,0,0.4)] rounded-xl
                    ${className || ''}`}>
      
      {actions.map((action) => (
        <ActionBtn
          key={action.id}
          color={action.color}
          icon={action.icon}
          label={action.label}
          badge={action.badge}
          onClick={action.onClick}
        />
      ))}
    </div>
  );
};