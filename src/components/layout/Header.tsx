import React, { useState } from 'react';
import { Search, Bell, User, Settings } from 'lucide-react';
// cn import removed as it's not used in this component

export const Header: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);

  return (
    <header className="glass-card border-b border-white/10 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex-1 max-w-md mx-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Zoek klanten, offertes, projecten..."
              className="glass-input w-full pl-10 pr-4 py-2 text-white placeholder-slate-400"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <StatusIndicator status="online" label="Rita AI" />
          <NotificationBell count={3} />
        </div>
      </div>
    </header>
  );
};
