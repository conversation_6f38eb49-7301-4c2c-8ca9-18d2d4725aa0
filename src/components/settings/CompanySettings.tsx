import React, { useState } from 'react';
import { 
  Building2, 
  Mail, 
  Phone, 
  MapPin, 
  Globe,
  FileText,
  Save,
  Upload,
  Camera,
  Edit
} from 'lucide-react';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@/components/ui/Button';
import { FormInput, FormFieldGroup } from '@/components/ui/Form';
import { useAppStore } from '@/stores/useAppStore';

export const CompanySettings: React.FC = () => {
  const { showToast } = useToast();
  const { companySettings, updateCompanySettings } = useAppStore();
  
  // Form state
  const [formData, setFormData] = useState({
    name: companySettings?.name || '',
    email: companySettings?.email || '',
    phone: companySettings?.phone || '',
    address: companySettings?.address || '',
    city: companySettings?.city || '',
    postalCode: companySettings?.postalCode || '',
    country: companySettings?.country || 'Nederland',
    kvkNumber: companySettings?.kvkNumber || '',
    btwNumber: companySettings?.btwNumber || '',
    website: companySettings?.website || '',
    description: companySettings?.description || '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(companySettings?.logo || null);

  // Handle form changes
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle logo upload
  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save
  const handleSave = async () => {
    setIsLoading(true);
    
    try {
      // Validate required fields
      if (!formData.name || !formData.email || !formData.phone) {
        throw new Error('Vul alle verplichte velden in');
      }

      // Update company settings in store
      updateCompanySettings({
        ...formData,
        logo: logoPreview,
      });
      
      // TODO: Save to backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast({
        type: 'success',
        title: 'Opgeslagen',
        message: 'Bedrijfsinformatie is succesvol opgeslagen.',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: error instanceof Error ? error.message : 'Er is een fout opgetreden bij het opslaan.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Building2 className="h-6 w-6 mr-3" />
            Bedrijfsinformatie
          </h2>
          <p className="text-gray-300">Beheer uw bedrijfsgegevens en contactinformatie</p>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? 'Opslaan...' : 'Opslaan'}
        </Button>
      </div>

      {/* Company Logo */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Camera className="h-5 w-5 mr-2" />
          Bedrijfslogo
        </h3>
        <div className="flex items-center space-x-6">
          <div className="w-24 h-24 bg-glass-dark rounded-lg flex items-center justify-center border-2 border-dashed border-white/20">
            {logoPreview ? (
              <img 
                src={logoPreview} 
                alt="Bedrijfslogo" 
                className="w-20 h-20 object-contain rounded"
              />
            ) : (
              <Camera className="h-8 w-8 text-gray-400" />
            )}
          </div>
          <div className="flex-1">
            <p className="text-sm text-gray-300 mb-2">
              Upload uw bedrijfslogo (PNG, JPG, max 2MB)
            </p>
            <div className="flex space-x-3">
              <label className="cursor-pointer">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <Button variant="glass" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Logo Uploaden
                </Button>
              </label>
              {logoPreview && (
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setLogoPreview(null)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Verwijderen
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Company Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Basic Information */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Building2 className="h-5 w-5 mr-2" />
            Basis Informatie
          </h3>
          <div className="space-y-4">
            <FormInput
              label="Bedrijfsnaam *"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Voer bedrijfsnaam in..."
              required
            />
            
            <FormInput
              label="Website"
              value={formData.website}
              onChange={(e) => handleChange('website', e.target.value)}
              placeholder="https://www.bedrijf.nl"
              leftIcon={<Globe className="h-4 w-4" />}
            />
            
            <FormInput
              label="Beschrijving"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="Korte beschrijving van uw bedrijf..."
              multiline
              rows={3}
            />
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Contact Informatie
          </h3>
          <div className="space-y-4">
            <FormInput
              label="Email *"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              placeholder="<EMAIL>"
              leftIcon={<Mail className="h-4 w-4" />}
              required
            />
            
            <FormInput
              label="Telefoon *"
              value={formData.phone}
              onChange={(e) => handleChange('phone', e.target.value)}
              placeholder="+31 20 123 4567"
              leftIcon={<Phone className="h-4 w-4" />}
              required
            />
          </div>
        </div>

        {/* Address Information */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Adres Informatie
          </h3>
          <div className="space-y-4">
            <FormInput
              label="Straat en huisnummer"
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              placeholder="Hoofdstraat 123"
            />
            
            <div className="grid grid-cols-2 gap-4">
              <FormInput
                label="Plaats"
                value={formData.city}
                onChange={(e) => handleChange('city', e.target.value)}
                placeholder="Amsterdam"
              />
              
              <FormInput
                label="Postcode"
                value={formData.postalCode}
                onChange={(e) => handleChange('postalCode', e.target.value)}
                placeholder="1234 AB"
              />
            </div>
            
            <FormInput
              label="Land"
              value={formData.country}
              onChange={(e) => handleChange('country', e.target.value)}
              placeholder="Nederland"
            />
          </div>
        </div>

        {/* Business Information */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Bedrijfsgegevens
          </h3>
          <div className="space-y-4">
            <FormInput
              label="KVK Nummer"
              value={formData.kvkNumber}
              onChange={(e) => handleChange('kvkNumber', e.target.value)}
              placeholder="12345678"
            />
            
            <FormInput
              label="BTW Nummer"
              value={formData.btwNumber}
              onChange={(e) => handleChange('btwNumber', e.target.value)}
              placeholder="NL123456789B01"
            />
          </div>
        </div>
      </div>

      {/* Preview Section */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Voorvertoning</h3>
        <div className="bg-glass-dark rounded-lg p-6">
          <div className="flex items-center mb-4">
            {logoPreview && (
              <img 
                src={logoPreview} 
                alt="Bedrijfslogo" 
                className="w-12 h-12 object-contain rounded mr-4"
              />
            )}
            <div>
              <h4 className="text-white font-semibold">
                {formData.name || 'Bedrijfsnaam'}
              </h4>
              <p className="text-gray-300 text-sm">
                {formData.description || 'Bedrijfsbeschrijving'}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-400">Email</p>
              <p className="text-white">{formData.email || '<EMAIL>'}</p>
            </div>
            <div>
              <p className="text-gray-400">Telefoon</p>
              <p className="text-white">{formData.phone || '+31 20 123 4567'}</p>
            </div>
            <div>
              <p className="text-gray-400">Adres</p>
              <p className="text-white">
                {formData.address && `${formData.address}, `}
                {formData.postalCode && `${formData.postalCode} `}
                {formData.city}
              </p>
            </div>
            <div>
              <p className="text-gray-400">Website</p>
              <p className="text-white">{formData.website || 'https://www.bedrijf.nl'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 