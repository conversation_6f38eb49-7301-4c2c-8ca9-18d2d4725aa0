import React, { useState } from 'react';
import { 
  User, 
  Bell, 
  Globe, 
  Palette, 
  Save,
  Eye,
  EyeOff,
  Mail,
  MessageSquare,
  Clock,
  Calendar,
  Euro,
  DollarSign
} from 'lucide-react';
import { useToast } from '@/contexts/ToastContext';
import { Button } from '@/components/ui/Button';
import { FormInput, FormSelect, FormFieldGroup, FormCheckbox } from '@/components/ui/Form';
import { useAppStore } from '@/stores/useAppStore';

// Language options
const languageOptions = [
  { value: 'nl', label: 'Nederlands' },
  { value: 'en', label: 'English' },
  { value: 'fr', label: 'Français' },
];

// Currency options
const currencyOptions = [
  { value: 'EUR', label: 'Euro (€)' },
  { value: 'USD', label: 'US Dollar ($)' },
];

// Date format options
const dateFormatOptions = [
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
];

// Time format options
const timeFormatOptions = [
  { value: '12h', label: '12-uurs (AM/PM)' },
  { value: '24h', label: '24-uurs' },
];

export const UserSettings: React.FC = () => {
  const { showToast } = useToast();
  const { user, updateUserPreferences } = useAppStore();
  
  // Form state
  const [formData, setFormData] = useState({
    theme: user?.preferences?.theme || 'dark',
    language: user?.preferences?.language || 'nl',
    notifications: user?.preferences?.notifications ?? true,
    emailNotifications: user?.preferences?.emailNotifications ?? true,
    smsNotifications: user?.preferences?.smsNotifications ?? false,
    currency: user?.preferences?.currency || 'EUR',
    dateFormat: user?.preferences?.dateFormat || 'DD/MM/YYYY',
    timeFormat: user?.preferences?.timeFormat || '24h',
    autoSave: user?.preferences?.autoSave ?? true,
    defaultQuoteTemplate: user?.preferences?.defaultQuoteTemplate || '',
    defaultProjectStatus: user?.preferences?.defaultProjectStatus || 'planning',
  });

  const [isLoading, setIsLoading] = useState(false);

  // Handle form changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle save
  const handleSave = async () => {
    setIsLoading(true);
    
    try {
      // Update preferences in store
      updateUserPreferences(formData);
      
      // TODO: Save to backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast({
        type: 'success',
        title: 'Opgeslagen',
        message: 'Uw voorkeuren zijn succesvol opgeslagen.',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het opslaan.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <User className="h-6 w-6 mr-3" />
            Gebruikersvoorkeuren
          </h2>
          <p className="text-gray-300">Beheer uw persoonlijke instellingen en voorkeuren</p>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? 'Opslaan...' : 'Opslaan'}
        </Button>
      </div>

      {/* Settings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Appearance Settings */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Palette className="h-5 w-5 mr-2" />
            Uiterlijk
          </h3>
          <div className="space-y-4">
            <FormSelect
              label="Thema"
              value={formData.theme}
              onChange={(e) => handleChange('theme', e.target.value)}
              options={[
                { value: 'dark', label: 'Donker' },
                { value: 'light', label: 'Licht' },
              ]}
            />
            
            <FormSelect
              label="Taal"
              value={formData.language}
              onChange={(e) => handleChange('language', e.target.value)}
              options={languageOptions}
            />
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            Notificaties
          </h3>
          <div className="space-y-4">
            <FormCheckbox
              label="Notificaties inschakelen"
              checked={formData.notifications}
              onChange={(e) => handleChange('notifications', e.target.checked)}
            />
            
            <FormCheckbox
              label="Email notificaties"
              checked={formData.emailNotifications}
              onChange={(e) => handleChange('emailNotifications', e.target.checked)}
              disabled={!formData.notifications}
            />
            
            <FormCheckbox
              label="SMS notificaties"
              checked={formData.smsNotifications}
              onChange={(e) => handleChange('smsNotifications', e.target.checked)}
              disabled={!formData.notifications}
            />
          </div>
        </div>

        {/* Regional Settings */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Globe className="h-5 w-5 mr-2" />
            Regionale Instellingen
          </h3>
          <div className="space-y-4">
            <FormSelect
              label="Valuta"
              value={formData.currency}
              onChange={(e) => handleChange('currency', e.target.value)}
              options={currencyOptions}
            />
            
            <FormSelect
              label="Datum formaat"
              value={formData.dateFormat}
              onChange={(e) => handleChange('dateFormat', e.target.value)}
              options={dateFormatOptions}
            />
            
            <FormSelect
              label="Tijd formaat"
              value={formData.timeFormat}
              onChange={(e) => handleChange('timeFormat', e.target.value)}
              options={timeFormatOptions}
            />
          </div>
        </div>

        {/* Application Settings */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Save className="h-5 w-5 mr-2" />
            Applicatie Instellingen
          </h3>
          <div className="space-y-4">
            <FormCheckbox
              label="Automatisch opslaan"
              checked={formData.autoSave}
              onChange={(e) => handleChange('autoSave', e.target.checked)}
            />
            
            <FormInput
              label="Standaard offerte template"
              value={formData.defaultQuoteTemplate}
              onChange={(e) => handleChange('defaultQuoteTemplate', e.target.value)}
              placeholder="Voer template naam in..."
            />
            
            <FormSelect
              label="Standaard project status"
              value={formData.defaultProjectStatus}
              onChange={(e) => handleChange('defaultProjectStatus', e.target.value)}
              options={[
                { value: 'planning', label: 'Planning' },
                { value: 'active', label: 'Actief' },
              ]}
            />
          </div>
        </div>
      </div>

      {/* Preview Section */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Voorvertoning</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-glass-dark rounded-lg">
            <div className="flex items-center mb-2">
              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
              <span className="text-sm text-gray-400">Datum</span>
            </div>
            <p className="text-white font-medium">
              {formData.dateFormat === 'DD/MM/YYYY' && '15/03/2024'}
              {formData.dateFormat === 'MM/DD/YYYY' && '03/15/2024'}
              {formData.dateFormat === 'YYYY-MM-DD' && '2024-03-15'}
            </p>
          </div>
          
          <div className="p-4 bg-glass-dark rounded-lg">
            <div className="flex items-center mb-2">
              <Clock className="h-4 w-4 mr-2 text-gray-400" />
              <span className="text-sm text-gray-400">Tijd</span>
            </div>
            <p className="text-white font-medium">
              {formData.timeFormat === '12h' ? '2:30 PM' : '14:30'}
            </p>
          </div>
          
          <div className="p-4 bg-glass-dark rounded-lg">
            <div className="flex items-center mb-2">
              {formData.currency === 'EUR' ? (
                <Euro className="h-4 w-4 mr-2 text-gray-400" />
              ) : (
                <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
              )}
              <span className="text-sm text-gray-400">Valuta</span>
            </div>
            <p className="text-white font-medium">
              {formData.currency === 'EUR' ? '€1,250.00' : '$1,250.00'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}; 