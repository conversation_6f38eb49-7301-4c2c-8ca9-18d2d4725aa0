import React, { useState, useEffect, useRef } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  DollarSign,
  User,
  FileText,
  Building,
  Tag,
  SortAsc,
  SortDesc,
  ChevronDown
} from 'lucide-react';
import { Button } from './Button';
import { FormInput, FormSelect } from './Form';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';

export interface SearchFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in';
  value: any;
  label?: string;
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
  label: string;
}

interface AdvancedSearchProps {
  placeholder?: string;
  searchFields: Array<{
    key: string;
    label: string;
    type: 'text' | 'number' | 'date' | 'select' | 'multiselect';
    options?: Array<{ value: string; label: string }>;
  }>;
  sortOptions: Array<{
    key: string;
    label: string;
  }>;
  onSearch: (query: string, filters: SearchFilter[], sort?: SortOption) => void;
  onClear: () => void;
  className?: string;
}

export const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  placeholder = "Zoeken...",
  searchFields,
  sortOptions,
  onSearch,
  onClear,
  className = "",
}) => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilter[]>([]);
  const [sortOption, setSortOption] = useState<SortOption | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  const [showSort, setShowSort] = useState(false);
  const filtersRef = useRef<HTMLDivElement>(null);
  const sortRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {
        setShowFilters(false);
      }
      if (sortRef.current && !sortRef.current.contains(event.target as Node)) {
        setShowSort(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Trigger search when query, filters, or sort changes
  useEffect(() => {
    onSearch(query, filters, sortOption);
  }, [query, filters, sortOption, onSearch]);

  const addFilter = (field: string) => {
    const searchField = searchFields.find(f => f.key === field);
    if (!searchField) return;

    const newFilter: SearchFilter = {
      field,
      operator: searchField.type === 'text' ? 'contains' : 'equals',
      value: '',
      label: searchField.label,
    };

    setFilters(prev => [...prev, newFilter]);
  };

  const updateFilter = (index: number, updates: Partial<SearchFilter>) => {
    setFilters(prev => 
      prev.map((filter, i) => 
        i === index ? { ...filter, ...updates } : filter
      )
    );
  };

  const removeFilter = (index: number) => {
    setFilters(prev => prev.filter((_, i) => i !== index));
  };

  const clearAll = () => {
    setQuery('');
    setFilters([]);
    setSortOption(undefined);
    onClear();
  };

  const getOperatorOptions = (type: string) => {
    switch (type) {
      case 'text':
        return [
          { value: 'contains', label: 'Bevat' },
          { value: 'equals', label: 'Is gelijk aan' },
          { value: 'startsWith', label: 'Begint met' },
          { value: 'endsWith', label: 'Eindigt met' },
        ];
      case 'number':
        return [
          { value: 'equals', label: 'Is gelijk aan' },
          { value: 'greaterThan', label: 'Groter dan' },
          { value: 'lessThan', label: 'Kleiner dan' },
          { value: 'between', label: 'Tussen' },
        ];
      case 'date':
        return [
          { value: 'equals', label: 'Op datum' },
          { value: 'greaterThan', label: 'Na datum' },
          { value: 'lessThan', label: 'Voor datum' },
          { value: 'between', label: 'Tussen datums' },
        ];
      case 'select':
      case 'multiselect':
        return [
          { value: 'equals', label: 'Is gelijk aan' },
          { value: 'in', label: 'Is een van' },
        ];
      default:
        return [{ value: 'equals', label: 'Is gelijk aan' }];
    }
  };

  const renderFilterValue = (filter: SearchFilter, index: number) => {
    const searchField = searchFields.find(f => f.key === filter.field);
    if (!searchField) return null;

    switch (searchField.type) {
      case 'text':
        return (
          <FormInput
            value={filter.value}
            onChange={(e) => updateFilter(index, { value: e.target.value })}
            placeholder="Waarde..."
            className="min-w-32"
          />
        );
      case 'number':
        return (
          <FormInput
            type="number"
            value={filter.value}
            onChange={(e) => updateFilter(index, { value: e.target.value })}
            placeholder="Getal..."
            className="min-w-32"
          />
        );
      case 'date':
        return (
          <FormInput
            type="date"
            value={filter.value}
            onChange={(e) => updateFilter(index, { value: e.target.value })}
            className="min-w-32"
          />
        );
      case 'select':
        return (
          <FormSelect
            value={filter.value}
            onChange={(e) => updateFilter(index, { value: e.target.value })}
            options={searchField.options || []}
            placeholder="Selecteer..."
            className="min-w-32"
          />
        );
      default:
        return null;
    }
  };

  const activeFiltersCount = filters.filter(f => f.value).length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Search Bar */}
      <div className="flex items-center space-x-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <FormInput
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={placeholder}
            className="pl-10"
          />
          {query && (
            <button
              onClick={() => setQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Filter Button */}
        <div className="relative" ref={filtersRef}>
          <Button
            variant="glass"
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {activeFiltersCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {activeFiltersCount}
              </span>
            )}
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>

          {/* Filters Dropdown */}
          {showFilters && (
            <div className="absolute top-full left-0 mt-2 w-96 bg-glass-dark backdrop-blur-md border border-white/20 rounded-lg shadow-xl z-50">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-white">Filters</h3>
                  {filters.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters([])}
                      className="text-gray-400 hover:text-white"
                    >
                      Wis alles
                    </Button>
                  )}
                </div>

                {/* Add Filter */}
                <div className="mb-4">
                  <FormSelect
                    placeholder="Filter toevoegen..."
                    value=""
                    onChange={(e) => {
                      if (e.target.value) {
                        addFilter(e.target.value);
                        e.target.value = '';
                      }
                    }}
                    options={searchFields.map(field => ({
                      value: field.key,
                      label: field.label,
                    }))}
                  />
                </div>

                {/* Active Filters */}
                <div className="space-y-3">
                  {filters.map((filter, index) => {
                    const searchField = searchFields.find(f => f.key === filter.field);
                    return (
                      <div key={index} className="bg-glass-light rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-white">
                            {filter.label}
                          </span>
                          <button
                            onClick={() => removeFilter(index)}
                            className="text-gray-400 hover:text-white"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <FormSelect
                            value={filter.operator}
                            onChange={(e) => updateFilter(index, { operator: e.target.value as any })}
                            options={getOperatorOptions(searchField?.type || 'text')}
                          />
                          {renderFilterValue(filter, index)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sort Button */}
        <div className="relative" ref={sortRef}>
          <Button
            variant="glass"
            onClick={() => setShowSort(!showSort)}
          >
            {sortOption ? (
              sortOption.direction === 'asc' ? <SortAsc className="h-4 w-4 mr-2" /> : <SortDesc className="h-4 w-4 mr-2" />
            ) : (
              <SortAsc className="h-4 w-4 mr-2" />
            )}
            Sorteren
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>

          {/* Sort Dropdown */}
          {showSort && (
            <div className="absolute top-full right-0 mt-2 w-48 bg-glass-dark backdrop-blur-md border border-white/20 rounded-lg shadow-xl z-50">
              <div className="p-2">
                {sortOptions.map((option) => (
                  <div key={option.key} className="space-y-1">
                    <button
                      onClick={() => {
                        setSortOption({ field: option.key, direction: 'asc', label: option.label });
                        setShowSort(false);
                      }}
                      className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded flex items-center"
                    >
                      <SortAsc className="h-4 w-4 mr-2" />
                      {option.label} (A-Z)
                    </button>
                    <button
                      onClick={() => {
                        setSortOption({ field: option.key, direction: 'desc', label: option.label });
                        setShowSort(false);
                      }}
                      className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded flex items-center"
                    >
                      <SortDesc className="h-4 w-4 mr-2" />
                      {option.label} (Z-A)
                    </button>
                  </div>
                ))}
                {sortOption && (
                  <div className="border-t border-white/20 mt-2 pt-2">
                    <button
                      onClick={() => {
                        setSortOption(undefined);
                        setShowSort(false);
                      }}
                      className="w-full text-left px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-white/10 rounded"
                    >
                      Sortering wissen
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Clear All */}
        {(query || filters.length > 0 || sortOption) && (
          <Button
            variant="ghost"
            onClick={clearAll}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4 mr-2" />
            Wissen
          </Button>
        )}
      </div>

      {/* Active Filters Summary */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.filter(f => f.value).map((filter, index) => (
            <div
              key={index}
              className="inline-flex items-center bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm"
            >
              <span>{filter.label}: {filter.value}</span>
              <button
                onClick={() => removeFilter(index)}
                className="ml-2 text-blue-300 hover:text-white"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
