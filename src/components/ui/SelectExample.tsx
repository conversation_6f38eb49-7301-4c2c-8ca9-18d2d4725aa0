import React, { useState } from 'react';
import { Select, SelectOption } from './Select';
import { User, Building, Home, Settings } from 'lucide-react';

// Voorbeeld data
const customerOptions: SelectOption[] = [
  { value: 'customer-1', label: '<PERSON>', icon: <User size={16} /> },
  { value: 'customer-2', label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Visser', icon: <Building size={16} /> },
  { value: 'customer-3', label: 'Woningbouw BV', icon: <Home size={16} /> },
  { value: 'customer-4', label: 'Renovatie Pro', icon: <Settings size={16} /> },
];

const statusOptions: SelectOption[] = [
  { value: 'draft', label: 'Concept' },
  { value: 'sent', label: 'Verzonden' },
  { value: 'accepted', label: 'Geaccepteerd' },
  { value: 'rejected', label: 'Afgewezen' },
  { value: 'expired', label: 'Verlopen' },
];

const serviceOptions: SelectOption[] = [
  { value: 'renovation', label: 'Renovatie' },
  { value: 'construction', label: 'Bouw' },
  { value: 'maintenance', label: 'Onderhoud' },
  { value: 'design', label: 'Ontwerp' },
  { value: 'consultation', label: 'Advies' },
];

export const SelectExample: React.FC = () => {
  const [selectedCustomer, setSelectedCustomer] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedSize, setSelectedSize] = useState<string>('md');

  return (
    <div className="p-6 space-y-6 bg-glass-dark rounded-lg">
      <h2 className="text-xl font-semibold text-white mb-4">
        Select Component Voorbeelden
      </h2>

      {/* Basis Select */}
      <div className="space-y-2">
        <label className="text-white text-sm font-medium">Klant Selecteren</label>
        <Select
          value={selectedCustomer}
          onChange={setSelectedCustomer}
          options={customerOptions}
          placeholder="Selecteer een klant"
          searchable
          clearable
          size="md"
        />
      </div>

      {/* Status Select met Error */}
      <div className="space-y-2">
        <label className="text-white text-sm font-medium">Quote Status</label>
        <Select
          value={selectedStatus}
          onChange={setSelectedStatus}
          options={statusOptions}
          placeholder="Selecteer status"
          error={!selectedStatus}
          helperText={!selectedStatus ? "Status is verplicht" : undefined}
        />
      </div>

      {/* Multi-Select */}
      <div className="space-y-2">
        <label className="text-white text-sm font-medium">Diensten (Meerdere selectie)</label>
        <Select
          value={selectedServices}
          onChange={setSelectedServices}
          options={serviceOptions}
          placeholder="Selecteer diensten"
          multiSelect
          searchable
          clearable
        />
      </div>

      {/* Size Variants */}
      <div className="space-y-2">
        <label className="text-white text-sm font-medium">Component Grootte</label>
        <Select
          value={selectedSize}
          onChange={setSelectedSize}
          options={[
            { value: 'sm', label: 'Klein' },
            { value: 'md', label: 'Medium' },
            { value: 'lg', label: 'Groot' },
          ]}
          placeholder="Selecteer grootte"
          size="md"
        />
      </div>

      {/* Geselecteerde Waarden Weergave */}
      <div className="mt-6 p-4 bg-white/5 rounded-lg">
        <h3 className="text-white font-medium mb-2">Geselecteerde Waarden:</h3>
        <div className="space-y-1 text-sm text-white/80">
          <p>Klant: {selectedCustomer || 'Niet geselecteerd'}</p>
          <p>Status: {selectedStatus || 'Niet geselecteerd'}</p>
          <p>Diensten: {selectedServices.length > 0 ? selectedServices.join(', ') : 'Geen geselecteerd'}</p>
          <p>Grootte: {selectedSize}</p>
        </div>
      </div>
    </div>
  );
}; 