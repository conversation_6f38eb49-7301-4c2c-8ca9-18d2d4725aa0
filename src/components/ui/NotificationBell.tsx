import React, { useState } from 'react';
import { Bell, X } from 'lucide-react';
import { cn } from '../../utils/cn';

interface NotificationBellProps {
  count: number;
}

export const NotificationBell = ({ count }: NotificationBellProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications] = useState([
    { id: 1, title: 'Nieuwe offerte aanvraag', message: '<PERSON> vraagt offerte aan', time: '2 min geleden' },
    { id: 2, title: 'Rita AI update', message: 'Nieuwe AI features beschikbaar', time: '1 uur geleden' },
    { id: 3, title: 'Factuur betaald', message: 'Factuur #2024-001 is betaald', time: '3 uur geleden' }
  ]);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 glass-button rounded-lg hover:scale-105 transition-transform"
      >
        <Bell className="w-5 h-5 text-white" />
        {count > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {count}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 top-12 w-80 glass-card border border-white/20 rounded-xl shadow-xl z-50">
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center justify-between">
              <h3 className="text-white font-semibold">Notificaties</h3>
              <button onClick={() => setIsOpen(false)}>
                <X className="w-4 h-4 text-slate-400" />
              </button>
            </div>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {notifications.map((notification) => (
              <div key={notification.id} className="p-4 border-b border-white/5 hover:bg-white/5">
                <h4 className="text-white text-sm font-medium">{notification.title}</h4>
                <p className="text-slate-400 text-xs mt-1">{notification.message}</p>
                <span className="text-slate-500 text-xs">{notification.time}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};