import React from 'react';
import { cn } from '@/utils/cn';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass';
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'rounded-lg border bg-background p-6 shadow-sm',
          variant === 'glass' && 'bg-glass border-white/20 backdrop-blur-sm',
          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = 'Card';

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description?: string;
}

const CardHeader = ({ title, description, className, ...props }: CardHeaderProps) => (
  <div className={cn('space-y-1 pb-4', className)} {...props}>
    <h3 className="text-lg font-semibold">{title}</h3>
    {description && <p className="text-sm text-muted-foreground">{description}</p>}
  </div>
);

const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('p-0', className)} {...props} />
  )
);
CardContent.displayName = 'CardContent';

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: 'start' | 'center' | 'end';
}

const CardFooter = ({ align = 'start', className, ...props }: CardFooterProps) => (
  <div
    className={cn(
      'flex items-center pt-4',
      align === 'center' && 'justify-center',
      align === 'end' && 'justify-end',
      className
    )}
    {...props}
  />
);

export { Card, CardHeader, CardContent, CardFooter };