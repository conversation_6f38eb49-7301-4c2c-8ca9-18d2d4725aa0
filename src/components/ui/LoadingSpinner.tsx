import React from 'react';
import { cn } from '@/utils/cn';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-8 w-8',
  lg: 'h-12 w-12',
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
}) => {
  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-white/20 border-t-primary-400',
          sizeClasses[size]
        )}
      />
    </div>
  );
};

export const LoadingSkeleton: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('animate-pulse bg-glass-dark rounded-lg', className)}>
      <div className="h-4 bg-white/10 rounded mb-2"></div>
      <div className="h-3 bg-white/10 rounded w-3/4"></div>
    </div>
  );
};

export const LoadingOverlay: React.FC<{ message?: string }> = ({ message = 'Laden...' }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-8 flex flex-col items-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className="text-white font-medium">{message}</p>
      </div>
    </div>
  );
}; 