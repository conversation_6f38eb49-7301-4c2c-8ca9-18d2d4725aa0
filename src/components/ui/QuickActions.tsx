import React, { useState } from 'react';
import { Plus, FileText, Users, Mic, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils/cn';

export const QuickActions = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const actions = [
    { icon: FileText, label: 'Nieuwe Offerte', path: '/quotes/new-ai', color: 'bg-blue-500' },
    { icon: Users, label: '<PERSON>eu<PERSON> Klant', path: '/customers/new', color: 'bg-green-500' },
    { icon: Mic, label: 'Rita AI', path: '/innovars-ai', color: 'bg-purple-500' }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {isOpen && (
        <div className="absolute bottom-16 right-0 space-y-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={() => {
                navigate(action.path);
                setIsOpen(false);
              }}
              className={cn(
                'flex items-center gap-3 px-4 py-3 rounded-full text-white shadow-lg',
                'transform transition-all duration-300 hover:scale-105',
                'glass-card border border-white/20',
                `animate-in slide-in-from-bottom-2 duration-${(index + 1) * 100}`
              )}
            >
              <action.icon className="w-5 h-5" />
              <span className="text-sm font-medium whitespace-nowrap">{action.label}</span>
            </button>
          ))}
        </div>
      )}
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-14 h-14 rounded-full shadow-lg transition-all duration-300',
          'glass-button border border-white/30 hover:scale-110',
          'flex items-center justify-center',
          isOpen && 'rotate-45'
        )}
      >
        {isOpen ? (
          <X className="w-6 h-6 text-white" />
        ) : (
          <Plus className="w-6 h-6 text-white" />
        )}
      </button>
    </div>
  );
};