import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '../../utils/cn';

interface TrendArrowProps {
  trend: 'up' | 'down' | 'stable';
  className?: string;
}

export const TrendArrow = ({ trend, className }: TrendArrowProps) => {
  const icons = {
    up: <TrendingUp className="w-4 h-4 text-green-400" />,
    down: <TrendingDown className="w-4 h-4 text-red-400" />,
    stable: <Minus className="w-4 h-4 text-slate-400" />
  };

  return (
    <div className={cn('p-1 rounded', className)}>
      {icons[trend]}
    </div>
  );
};