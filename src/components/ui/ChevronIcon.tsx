import React from 'react';
import { ChevronLeft } from 'lucide-react';
import { cn } from '@/utils/cn';

interface ChevronIconProps {
  isCollapsed: boolean;
  className?: string;
}

export const ChevronIcon: React.FC<ChevronIconProps> = ({ 
  isCollapsed, 
  className 
}) => {
  return (
    <ChevronLeft 
      className={cn(
        'h-4 w-4 transition-transform duration-500 ease-out',
        'will-change-transform',
        isCollapsed && 'rotate-180',
        className
      )} 
    />
  );
}; 