import React from 'react';
import { cn } from '@/utils/cn';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'text' | 'title' | 'avatar' | 'button' | 'card' | 'table-row' | 'list-item';
  lines?: number;
  width?: string;
  height?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'default',
  lines = 1,
  width,
  height,
  className,
  ...props
}) => {
  const baseClasses = 'animate-pulse bg-gray-600/50 rounded';

  const variants = {
    default: 'h-4 w-full',
    text: 'h-4 w-full',
    title: 'h-6 w-3/4',
    avatar: 'h-10 w-10 rounded-full',
    button: 'h-10 w-24',
    card: 'h-32 w-full rounded-lg',
    'table-row': 'h-12 w-full',
    'list-item': 'h-16 w-full rounded-lg',
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2" {...props}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variants[variant],
              index === lines - 1 ? 'w-3/4' : 'w-full',
              className
            )}
            style={{ width, height }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variants[variant], className)}
      style={{ width, height }}
      {...props}
    />
  );
};

// Specialized skeleton components
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('bg-glass-light border border-white/20 rounded-xl p-6', className)}>
    <div className="space-y-4">
      <Skeleton variant="title" className="w-1/2" />
      <Skeleton variant="text" lines={3} />
      <div className="flex justify-between items-center">
        <Skeleton variant="button" className="w-20" />
        <Skeleton variant="avatar" />
      </div>
    </div>
  </div>
);

export const SkeletonTable: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-2">
    {/* Header */}
    <div className="flex space-x-4 p-4 bg-glass-dark rounded-lg">
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton key={index} variant="text" className="flex-1" />
      ))}
    </div>
    
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4 p-4 bg-glass-light rounded-lg">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} variant="text" className="flex-1" />
        ))}
      </div>
    ))}
  </div>
);

export const SkeletonList: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="space-y-3">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="flex items-center space-x-4 p-4 bg-glass-light rounded-lg">
        <Skeleton variant="avatar" />
        <div className="flex-1 space-y-2">
          <Skeleton variant="title" className="w-1/3" />
          <Skeleton variant="text" className="w-2/3" />
        </div>
        <Skeleton variant="button" className="w-16" />
      </div>
    ))}
  </div>
);

export const SkeletonDashboard: React.FC = () => (
  <div className="space-y-6">
    {/* KPI Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>

    {/* Recent Activity */}
    <div className="bg-glass-light border border-white/20 rounded-xl p-6">
      <Skeleton variant="title" className="w-1/3 mb-4" />
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex items-start space-x-4">
            <Skeleton variant="avatar" className="w-8 h-8" />
            <div className="flex-1 space-y-2">
              <Skeleton variant="text" className="w-1/2" />
              <Skeleton variant="text" className="w-3/4" />
            </div>
          </div>
        ))}
      </div>
    </div>

    {/* Quick Actions */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Array.from({ length: 3 }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>
  </div>
);

export const SkeletonForm: React.FC = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-4">
        <Skeleton variant="text" className="w-1/4" />
        <Skeleton variant="button" className="w-full h-10" />
      </div>
      <div className="space-y-4">
        <Skeleton variant="text" className="w-1/4" />
        <Skeleton variant="button" className="w-full h-10" />
      </div>
    </div>
    
    <div className="space-y-4">
      <Skeleton variant="text" className="w-1/4" />
      <Skeleton variant="button" className="w-full h-24" />
    </div>
    
    <div className="flex justify-end space-x-3">
      <Skeleton variant="button" className="w-24 h-10" />
      <Skeleton variant="button" className="w-24 h-10" />
    </div>
  </div>
); 