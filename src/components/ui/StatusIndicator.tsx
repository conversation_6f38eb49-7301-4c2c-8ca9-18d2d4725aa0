interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'busy';
  label: string;
  showPulse?: boolean;
}

export const StatusIndicator = ({ status, label, showPulse = true }: StatusIndicatorProps) => {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-red-500', 
    busy: 'bg-yellow-500'
  };

  return (
    <div className="flex items-center gap-2">
      <div className="relative">
        <div className={cn('w-2 h-2 rounded-full', statusColors[status])} />
        {showPulse && status === 'online' && (
          <div className="absolute inset-0 w-2 h-2 bg-green-500 rounded-full animate-ping opacity-75" />
        )}
      </div>
      <span className="text-sm text-slate-300">{label}</span>
    </div>
  );
};