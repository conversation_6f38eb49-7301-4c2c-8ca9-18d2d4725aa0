import React, { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  TrendingUp, 
  FileText, 
  Users,
  Activity,
  Target,
  Clock,
  Star
} from 'lucide-react';
import { Card, CardHeader } from '@/components/ui/Card';
import { getRevenueTrend, getQuoteStatus, getCustomerGrowth } from '@/api/dashboard';

// Define specific types for chart data
interface LineChartDataPoint {
  month: string;
  revenue?: number;
  new?: number;
  total?: number;
}

interface PieChartDataPoint {
  name: string;
  value: number;
  color: string;
}

// Simple chart component (placeholder for a real chart library)
const SimpleLineChart: React.FC<{ data: LineChartDataPoint[]; height?: number }> = ({ data, height = 200 }) => {
  if (!data || data.length === 0) {
    return <div style={{ height }} className="flex items-center justify-center text-white/50">Geen data</div>;
  }
  const values = data.map(d => d.revenue ?? d.new ?? d.total ?? 0);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const range = maxValue - minValue;

  const getY = (value: number) => {
    if (range === 0) {
      return 50; // Center vertically
    }
    return 100 - ((value - minValue) / range) * 100;
  };

  return (
    <div className="relative" style={{ height }}>
      <svg width="100%" height="100%" className="absolute inset-0">
        <path
          d={data.map((point, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = getY(point.revenue ?? point.new ?? point.total ?? 0);
            return `${index === 0 ? 'M' : 'L'} ${x}% ${y}%`;
          }).join(' ')}
          stroke="#10B981"
          strokeWidth="2"
          fill="none"
          className="drop-shadow-lg"
        />
        <defs>
          <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#10B981" stopOpacity="0.3" />
            <stop offset="100%" stopColor="#10B981" stopOpacity="0.1" />
          </linearGradient>
        </defs>
        <path
          d={`M 0 100 ${data.map((point, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = getY(point.revenue ?? point.new ?? point.total ?? 0);
            return `L ${x}% ${y}%`;
          }).join(' ')} L 100% 100 Z`}
          fill="url(#chartGradient)"
        />
      </svg>
      
      {data.map((point, index) => {
        const x = (index / (data.length - 1)) * 100;
        const y = getY(point.revenue ?? point.new ?? point.total ?? 0);
        return (
          <circle
            key={index}
            cx={`${x}%`}
            cy={`${y}%`}
            r="3"
            fill="#10B981"
            className="drop-shadow-sm"
          />
        );
      })}
    </div>
  );
};

const SimplePieChart: React.FC<{ data: PieChartDataPoint[] }> = ({ data }) => {
  if (!data || data.length === 0) {
      return <div className="flex items-center justify-center text-white/50 h-32">Geen data</div>;
  }
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = 0;

  return (
    <div className="relative w-32 h-32 mx-auto">
      <svg width="100%" height="100%" viewBox="0 0 100 100">
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100;
          const angle = (percentage / 100) * 360;
          const x1 = 50 + 40 * Math.cos((currentAngle * Math.PI) / 180);
          const y1 = 50 + 40 * Math.sin((currentAngle * Math.PI) / 180);
          const x2 = 50 + 40 * Math.cos(((currentAngle + angle) * Math.PI) / 180);
          const y2 = 50 + 40 * Math.sin(((currentAngle + angle) * Math.PI) / 180);
          
          const largeArcFlag = angle > 180 ? 1 : 0;
          
          const pathData = [
            `M 50 50`,
            `L ${x1} ${y1}`,
            `A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            'Z'
          ].join(' ');
          
          currentAngle += angle;
          
          return (
            <path
              key={index}
              d={pathData}
              fill={item.color}
              className="transition-all duration-300 hover:opacity-80"
            />
          );
        })}
      </svg>
    </div>
  );
};

export const InteractiveCharts: React.FC = () => {
  const [selectedPeriod, _setSelectedPeriod] = useState<'6m' | '12m'>('12m');
  const [selectedMetric, _setSelectedMetric] = useState<'revenue' | 'customers'>('revenue');

  const { data: revenueData = [] } = useQuery<LineChartDataPoint[], Error>({
    queryKey: ['revenueTrend', selectedPeriod],
    queryFn: () => getRevenueTrend(selectedPeriod),
  });

  const { data: quoteStatusData = [] } = useQuery<PieChartDataPoint[], Error>({
    queryKey: ['quoteStatus'],
    queryFn: getQuoteStatus,
  });

  const { data: customerGrowthData = [] } = useQuery<LineChartDataPoint[], Error>({
    queryKey: ['customerGrowth', selectedPeriod],
    queryFn: () => getCustomerGrowth(selectedPeriod),
  });

  const getChartData = useCallback(() => {
    switch (selectedMetric) {
      case 'revenue':
        return revenueData;
      case 'customers':
        return customerGrowthData;
      default:
        return revenueData;
    }
  }, [selectedMetric, revenueData, customerGrowthData]);

  const _getMetricValue = useCallback((metric: keyof LineChartDataPoint) => {
    const data = getChartData();
    if (data && data.length > 0) {
      const lastPoint = data[data.length - 1];
      // Use a type guard to ensure the property exists and is a number
      if (lastPoint && typeof lastPoint[metric] === 'number') {
        return lastPoint[metric] as number;
      }
    }
    return 0;
  }, [getChartData]);

  const mainChartData = getChartData();
  const lastCustomerPoint = customerGrowthData && customerGrowthData.length > 0 ? customerGrowthData[customerGrowthData.length - 1] : null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Chart */}
      <Card className="lg:col-span-2 bg-glass-dark backdrop-blur-sm border border-white/20">
        <CardHeader title="" className="pb-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-semibold">
                  {selectedMetric === 'revenue' ? 'Omzet Trend' : 'Klantengroei'}
                </h3>
                <p className="text-white/60 text-sm">Laatste {selectedPeriod === '6m' ? '6' : '12'} maanden</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {/* Period Toggle Placeholder */}
            </div>
          </div>
        </CardHeader>
        <div className="p-4 pt-0">
          <div className="h-64">
            <SimpleLineChart data={mainChartData} height={256} />
          </div>
        </div>
      </Card>

      {/* Quote Status */}
      <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
        <CardHeader title="" className="pb-4">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-white font-semibold">Offertestatus</h3>
              <p className="text-white/60 text-sm">Huidige maand</p>
            </div>
          </div>
          <div className="flex items-center justify-center my-4">
            <SimplePieChart data={quoteStatusData} />
          </div>
          <div className="space-y-2 text-sm">
            {quoteStatusData.map((item) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 rounded-full" style={{ backgroundColor: item.color }}></span>
                  <span className="text-white/80">{item.name}</span>
                </div>
                <span className="font-medium text-white">{item.value}</span>
              </div>
            ))}
          </div>
        </CardHeader>
      </Card>

      {/* Customer Growth */}
      <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
        <CardHeader title="" className="pb-4">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-white font-semibold">Klantengroei</h3>
              <p className="text-white/60 text-sm">Totaal en nieuwe klanten</p>
            </div>
          </div>

          <div className="h-48">
            <SimpleLineChart data={customerGrowthData} height={192} />
          </div>

          {/* Growth metrics */}
          <div className="mt-4 grid grid-cols-2 gap-4 text-center text-sm">
            <div>
              <div className="text-white font-medium">
                {lastCustomerPoint?.new ?? 0}
              </div>
              <div className="text-white/60">Nieuwe deze maand</div>
            </div>
            <div>
              <div className="text-white font-medium">
                {Math.round(customerGrowthData.reduce((sum, d) => sum + (d.new ?? 0), 0) / (customerGrowthData.length || 1))}
              </div>
              <div className="text-white/60">Gemiddelde per maand</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Performance Metrics */}
      <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
        <CardHeader title="" className="pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-semibold">Performance Metrics</h3>
                <p className="text-white/60 text-sm">KPI overzicht</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-green-400" />
                </div>
                <div>
                  <div className="text-white font-medium">Conversie Rate</div>
                  <div className="text-white/60 text-sm">Quote → Klant</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white font-bold text-lg">68%</div>
                <div className="text-green-400 text-sm">+6%</div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Clock className="w-4 h-4 text-blue-400" />
                </div>
                <div>
                  <div className="text-white font-medium">Response Time</div>
                  <div className="text-white/60 text-sm">Gemiddelde</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white font-bold text-lg">2.4h</div>
                <div className="text-green-400 text-sm">-0.7h</div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <Star className="w-4 h-4 text-purple-400" />
                </div>
                <div>
                  <div className="text-white font-medium">Klanttevredenheid</div>
                  <div className="text-white/60 text-sm">Gemiddelde rating</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white font-bold text-lg">4.6/5</div>
                <div className="text-green-400 text-sm">+0.2</div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>
    </div>
  );
}; 