import React, { useState } from 'react';
import { Bot, TrendingUp, FileText, Users, Settings, X, Send, Mic } from 'lucide-react';

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

const aiSuggestions = [
  {
    id: '1',
    title: 'Quote Optimalisatie',
    description: 'Analyseer je beste quotes en krijg tips voor hogere conversie',
    icon: TrendingUp,
    color: 'bg-blue-600/20 text-blue-400'
  },
  {
    id: '2',
    title: 'Klant Insights',
    description: 'Ontdek patronen in je klantgedrag en verkoopkansen',
    icon: Users,
    color: 'bg-purple-600/20 text-purple-400'
  },
  {
    id: '3',
    title: 'Prijzen Strategie',
    description: 'Krijg AI-gedreven prijsaanbevelingen voor je services',
    icon: FileText,
    color: 'bg-green-600/20 text-green-400'
  },
  {
    id: '4',
    title: 'Workflow Optimalisatie',
    description: 'Automatiseer repetitieve taken en verhoog productiviteit',
    icon: Settings,
    color: 'bg-orange-600/20 text-orange-400'
  }
];

export const AIAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<AIMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hallo! Ik ben je AI assistent. Hoe kan ik je vandaag helpen met je quotes en klanten?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) {return;}

    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: getAIResponse(inputValue),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000);
  };

  const getAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('quote') || input.includes('offerte')) {
      return 'Ik zie dat je vragen hebt over quotes. Ik kan je helpen met het optimaliseren van je offertes, prijsstrategieën en conversiepercentages. Wat specifiek wil je weten?';
    }
    
    if (input.includes('klant') || input.includes('customer')) {
      return 'Voor klantmanagement kan ik je helpen met lead scoring, klantsegmentatie en verkoopkansen identificeren. Heb je een specifieke vraag?';
    }
    
    if (input.includes('prijs') || input.includes('price')) {
      return 'Voor prijsstrategieën analyseer ik markttrends en concurrentie. Ik kan je helpen met dynamische prijzen en bundling strategieën.';
    }
    
    return 'Interessante vraag! Ik kan je helpen met quotes, klanten, prijzen, workflows en meer. Wat wil je precies weten?';
  };

  const handleSuggestionClick = (suggestion: typeof aiSuggestions[0]) => {
    const message: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: `Vertel me meer over ${suggestion.title.toLowerCase()}`,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
    setIsTyping(true);

    setTimeout(() => {
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `Geweldig! Voor ${suggestion.title} kan ik je helpen met geavanceerde analyses en aanbevelingen. Laat me je meer vertellen over hoe we dit kunnen implementeren in je workflow.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* AI Assistant Button */}
      {!isOpen && (
                 <button
           onClick={() => setIsOpen(true)}
           className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
           aria-label="Open AI Assistant"
           title="Open AI Assistant"
         >
           <Bot className="w-6 h-6" />
         </button>
      )}

      {/* AI Assistant Chat Window */}
      {isOpen && (
        <div className="bg-slate-800/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-slate-700 w-96 h-[600px] flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-slate-700">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-semibold">AI Assistant</h3>
                <p className="text-slate-400 text-xs">Online & Ready</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-slate-400 hover:text-white transition-colors"
              aria-label="Sluit AI Assistant"
              title="Sluit AI Assistant"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 text-slate-200'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-slate-700 text-slate-200 p-3 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce animate-delay-100" />
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce animate-delay-200" />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Suggestions */}
          {messages.length === 1 && (
            <div className="p-4 border-t border-slate-700">
              <p className="text-slate-400 text-xs mb-3">💡 Populaire vragen:</p>
              <div className="grid grid-cols-2 gap-2">
                {aiSuggestions.map((suggestion) => {
                  const Icon = suggestion.icon;
                  return (
                    <button
                      key={suggestion.id}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className={`${suggestion.color} p-3 rounded-lg text-left hover:scale-105 transition-all duration-200`}
                    >
                      <Icon className="w-4 h-4 mb-1" />
                      <p className="text-xs font-medium">{suggestion.title}</p>
                      <p className="text-xs opacity-70 mt-1">{suggestion.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t border-slate-700">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Stel een vraag aan je AI assistent..."
                className="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white p-2 rounded-lg transition-colors"
                aria-label="Verstuur bericht"
                title="Verstuur bericht"
              >
                <Send className="w-4 h-4" />
              </button>
              <button 
                className="bg-slate-700 hover:bg-slate-600 text-slate-400 p-2 rounded-lg transition-colors"
                aria-label="Spraak invoer"
                title="Spraak invoer"
              >
                <Mic className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 