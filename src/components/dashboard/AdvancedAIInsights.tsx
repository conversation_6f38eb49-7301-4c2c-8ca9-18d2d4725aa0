import React, { useState } from 'react';
import { 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb, 
  BarChart3,
  Eye,
  Clock,
  DollarSign,
  Users,
  Target,
  Zap,
  ArrowRight,
  CheckCircle,
  X
} from 'lucide-react';
import { Card, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';

interface AIInsight {
  id: string;
  type: 'opportunity' | 'warning' | 'recommendation' | 'trend';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  action?: {
    label: string;
    callback: () => void;
  };
  data?: {
    value: number;
    change: number;
    period: string;
  };
  timestamp: Date;
  read: boolean;
  category: 'pricing' | 'customer' | 'efficiency' | 'market' | 'revenue';
}

const insights: AIInsight[] = [
  {
    id: '1',
    type: 'opportunity',
    priority: 'high',
    title: 'Upselling Kans',
    description: '<PERSON><PERSON> "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" heeft interesse in zonnepanelen getoond tijdens renovatie project',
    action: { label: 'Bekijk Quote', callback: () => console.log('View quote') },
    data: { value: 15000, change: 0, period: 'potential' },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    read: false,
    category: 'revenue'
  },
  {
    id: '2',
    type: 'warning', 
    priority: 'urgent',
    title: 'Quote Vervalt Binnenkort',
    description: '3 quotes verlopen binnen 48 uur. Actie vereist om conversie te behouden',
    action: { label: 'Toon Quotes', callback: () => console.log('Show quotes') },
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    read: false,
    category: 'customer'
  },
  {
    id: '3',
    type: 'recommendation',
    priority: 'medium', 
    title: 'Optimale Prijsstelling',
    description: 'Je keukenrenovatie prijzen zijn 8% onder marktgemiddelde. Verhooging mogelijk zonder impact op conversie',
    data: { value: 12500, change: 8, period: 'market_avg' },
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    read: true,
    category: 'pricing'
  },
  {
    id: '4',
    type: 'trend',
    priority: 'low',
    title: 'Seizoenspatroon Gedetecteerd',
    description: 'Renovatie projecten pieken in Q2. Voorbereiden voor verhoogde vraag',
    data: { value: 35, change: 12, period: 'q2_forecast' },
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    read: true,
    category: 'market'
  },
  {
    id: '5',
    type: 'opportunity',
    priority: 'high',
    title: 'Klant Retentie Kans',
    description: '5 klanten van vorig jaar hebben geen nieuwe projecten. Follow-up campagne aanbevolen',
    action: { label: 'Start Campagne', callback: () => console.log('Start campaign') },
    data: { value: 45000, change: 0, period: 'potential' },
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    read: false,
    category: 'customer'
  },
  {
    id: '6',
    type: 'recommendation',
    priority: 'medium',
    title: 'Workflow Optimalisatie',
    description: 'Quote proces kan 30% sneller met AI assistentie. Automatiseer repetitieve taken',
    data: { value: 18, change: 30, period: 'time_saved' },
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    read: true,
    category: 'efficiency'
  }
];

const getInsightIcon = (type: AIInsight['type']) => {
  switch (type) {
    case 'opportunity':
      return <TrendingUp className="w-5 h-5" />;
    case 'warning':
      return <AlertTriangle className="w-5 h-5" />;
    case 'recommendation':
      return <Lightbulb className="w-5 h-5" />;
    case 'trend':
      return <BarChart3 className="w-5 h-5" />;
  }
};

const getPriorityColor = (priority: AIInsight['priority']) => {
  switch (priority) {
    case 'urgent':
      return 'border-red-500/50 bg-red-500/10';
    case 'high':
      return 'border-orange-500/50 bg-orange-500/10';
    case 'medium':
      return 'border-yellow-500/50 bg-yellow-500/10';
    case 'low':
      return 'border-blue-500/50 bg-blue-500/10';
  }
};

const getTypeColor = (type: AIInsight['type']) => {
  switch (type) {
    case 'opportunity':
      return 'text-green-400';
    case 'warning':
      return 'text-red-400';
    case 'recommendation':
      return 'text-yellow-400';
    case 'trend':
      return 'text-blue-400';
  }
};

const formatTimeAgo = (timestamp: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} min geleden`;
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} uur geleden`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `${days} dagen geleden`;
  }
};

export const AdvancedAIInsights: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'unread' | 'high' | 'urgent'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = ['all', 'pricing', 'customer', 'efficiency', 'market', 'revenue'];
  const categoryLabels = {
    all: 'Alles',
    pricing: 'Prijzen',
    customer: 'Klanten',
    efficiency: 'Efficiëntie',
    market: 'Markt',
    revenue: 'Omzet'
  };

  const filteredInsights = insights.filter(insight => {
    if (selectedFilter === 'unread' && insight.read) {return false;}
    if (selectedFilter === 'high' && insight.priority !== 'high') {return false;}
    if (selectedFilter === 'urgent' && insight.priority !== 'urgent') {return false;}
    if (selectedCategory !== 'all' && insight.category !== selectedCategory) {return false;}
    return true;
  });

  const unreadCount = insights.filter(i => !i.read).length;
  const urgentCount = insights.filter(i => i.priority === 'urgent').length;

  return (
    <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
      <CardHeader title="" className="pb-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-white font-semibold text-lg">AI Insights</h3>
              <p className="text-white/60 text-sm">Slimme aanbevelingen & kansen</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount} nieuw
              </span>
            )}
            {urgentCount > 0 && (
              <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                {urgentCount} urgent
              </span>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-6">
          {['all', 'unread', 'high', 'urgent'].map((filter) => (
            <button
              key={filter}
              onClick={() => setSelectedFilter(filter as any)}
              className={cn(
                'px-3 py-1.5 text-sm rounded-lg transition-colors',
                selectedFilter === filter
                  ? 'bg-blue-600 text-white'
                  : 'bg-white/5 text-white/60 hover:text-white hover:bg-white/10'
              )}
            >
              {filter === 'all' && 'Alles'}
              {filter === 'unread' && `Ongelezen (${unreadCount})`}
              {filter === 'high' && 'Hoog'}
              {filter === 'urgent' && `Urgent (${urgentCount})`}
            </button>
          ))}
        </div>

        {/* Category filters */}
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={cn(
                'px-3 py-1.5 text-sm rounded-lg transition-colors',
                selectedCategory === category
                  ? 'bg-purple-600 text-white'
                  : 'bg-white/5 text-white/60 hover:text-white hover:bg-white/10'
              )}
            >
              {categoryLabels[category as keyof typeof categoryLabels]}
            </button>
          ))}
        </div>

        {/* Insights List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {filteredInsights.length === 0 ? (
            <div className="text-center py-8">
              <Lightbulb className="w-12 h-12 text-white/20 mx-auto mb-4" />
              <p className="text-white/60">Geen insights gevonden</p>
            </div>
          ) : (
            filteredInsights.map((insight) => (
              <div
                key={insight.id}
                className={cn(
                  'p-4 rounded-lg border transition-all duration-200 hover:shadow-lg cursor-pointer',
                  getPriorityColor(insight.priority),
                  !insight.read && 'border-l-4 border-l-blue-500'
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={cn('p-2 rounded-lg bg-white/10', getTypeColor(insight.type))}>
                      {getInsightIcon(insight.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-white font-medium text-sm">
                          {insight.title}
                        </h4>
                        {insight.read && (
                          <CheckCircle className="w-4 h-4 text-green-400" />
                        )}
                        <span className={cn(
                          'text-xs px-2 py-0.5 rounded-full',
                          insight.priority === 'urgent' && 'bg-red-500/20 text-red-300',
                          insight.priority === 'high' && 'bg-orange-500/20 text-orange-300',
                          insight.priority === 'medium' && 'bg-yellow-500/20 text-yellow-300',
                          insight.priority === 'low' && 'bg-blue-500/20 text-blue-300'
                        )}>
                          {insight.priority}
                        </span>
                      </div>
                      
                      <p className="text-white/70 text-sm mb-3 leading-relaxed">
                        {insight.description}
                      </p>
                      
                      {insight.data && (
                        <div className="flex items-center space-x-4 text-xs mb-3">
                          <div className="flex items-center space-x-1">
                            <DollarSign className="w-3 h-3 text-green-400" />
                            <span className="text-white">€{insight.data.value.toLocaleString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="w-3 h-3 text-blue-400" />
                            <span className="text-white">{insight.data.change}%</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3 text-gray-400" />
                            <span className="text-white/60">{insight.data.period}</span>
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <span className="text-white/40 text-xs flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatTimeAgo(insight.timestamp)}
                        </span>
                        
                        {insight.action && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              insight.action?.callback();
                            }}
                            className="text-blue-400 text-xs flex items-center gap-1 hover:text-blue-300 transition-colors"
                          >
                            {insight.action.label}
                            <ArrowRight className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Mark as read
                    }}
                    className="text-white/40 hover:text-white transition-colors"
                    title="Markeer als gelezen"
                    aria-label="Markeer als gelezen"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-white font-bold text-lg">{insights.length}</div>
              <div className="text-white/60 text-xs">Totaal</div>
            </div>
            <div>
              <div className="text-green-400 font-bold text-lg">{insights.filter(i => i.type === 'opportunity').length}</div>
              <div className="text-white/60 text-xs">Kansen</div>
            </div>
            <div>
              <div className="text-orange-400 font-bold text-lg">{urgentCount}</div>
              <div className="text-white/60 text-xs">Urgent</div>
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}; 