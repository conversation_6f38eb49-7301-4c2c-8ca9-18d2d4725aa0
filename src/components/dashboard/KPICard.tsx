interface KPICardProps {
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
}

export const KPICard = ({ title, value, change, trend, icon }: KPICardProps) => {
  return (
    <div className="glass-card p-6 hover:bg-white/15 transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-primary-500/20 rounded-lg">
          {icon}
        </div>
        <TrendArrow trend={trend} />
      </div>
      <h3 className="text-2xl font-bold text-white mb-1">{value}</h3>
      <p className="text-slate-400 text-sm mb-2">{title}</p>
      <div className="flex items-center gap-1">
        <span className={cn(
          'text-sm font-medium',
          change > 0 ? 'text-green-400' : 'text-red-400'
        )}>
          {change > 0 ? '+' : ''}{change}%
        </span>
        <span className="text-slate-500 text-xs">vs vorige maand</span>
      </div>
    </div>
  );
};