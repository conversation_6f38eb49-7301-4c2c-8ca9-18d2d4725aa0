import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  DollarSign, 
  Users, 
  FileText,
  Sparkles,
  Target,
  Zap,
  Eye,
  MessageSquare,
  Calendar,
  Star,
  ArrowUp,
  ArrowDown,
  X
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'success' | 'warning' | 'info' | 'opportunity';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  priority: 'high' | 'medium' | 'low';
  timestamp: Date;
  action?: string;
  metric?: {
    value: string;
    change: number;
    trend: 'up' | 'down';
  };
}

const mockInsights: AIInsight[] = [
  {
    id: '1',
    type: 'opportunity',
    title: 'Hoge Conversie Kans',
    description: '<PERSON><PERSON> "<PERSON>" toont sterke interesse. AI voorspelt 85% kans op acceptatie.',
    icon: Target,
    priority: 'high',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min geleden
    action: 'Neem contact op',
    metric: {
      value: '85%',
      change: 12,
      trend: 'up'
    }
  },
  {
    id: '2',
    type: 'warning',
    title: 'Prijs Optimalisatie Nodig',
    description: 'Je quotes zijn 15% hoger dan marktgemiddelde. AI suggereert prijsverlaging voor betere conversie.',
    icon: AlertTriangle,
    priority: 'medium',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 uur geleden
    action: 'Bekijk prijsstrategie'
  },
  {
    id: '3',
    type: 'success',
    title: 'Uitstekende Klanttevredenheid',
    description: 'Gemiddelde klantscore is 4.8/5. Dit is 20% hoger dan vorige maand.',
    icon: Star,
    priority: 'medium',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 uur geleden
    metric: {
      value: '4.8/5',
      change: 20,
      trend: 'up'
    }
  },
  {
    id: '4',
    type: 'info',
    title: 'Nieuwe Markt Kans',
    description: 'AI detecteert groeiende vraag in renovatie sector. 3 potentiële leads geïdentificeerd.',
    icon: Sparkles,
    priority: 'high',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 uur geleden
    action: 'Bekijk leads'
  },
  {
    id: '5',
    type: 'opportunity',
    title: 'Automatisering Mogelijkheid',
    description: 'Repetitieve taken kunnen geautomatiseerd worden. Bespaar 8 uur/week.',
    icon: Zap,
    priority: 'medium',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 uur geleden
    action: 'Setup automatisering'
  }
];

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'border-red-500/50 bg-red-500/10';
    case 'medium': return 'border-yellow-500/50 bg-yellow-500/10';
    case 'low': return 'border-blue-500/50 bg-blue-500/10';
    default: return 'border-slate-500/50 bg-slate-500/10';
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'success': return 'text-green-400';
    case 'warning': return 'text-yellow-400';
    case 'info': return 'text-blue-400';
    case 'opportunity': return 'text-purple-400';
    default: return 'text-slate-400';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'success': return CheckCircle;
    case 'warning': return AlertTriangle;
    case 'info': return Eye;
    case 'opportunity': return Target;
    default: return Bell;
  }
};

interface AIInsightsPanelProps {
  onClose?: () => void;
}

export const AIInsightsPanel: React.FC<AIInsightsPanelProps> = ({ onClose }) => {
  const [insights, setInsights] = useState<AIInsight[]>(mockInsights);
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeFilter, setActiveFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [open, setOpen] = useState(true);

  if (!open) {return null;}

  const filteredInsights = insights.filter(insight => 
    activeFilter === 'all' || insight.priority === activeFilter
  );

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) {return `${days} dag${days > 1 ? 'en' : ''} geleden`;}
    if (hours > 0) {return `${hours} uur geleden`;}
    if (minutes > 0) {return `${minutes} min geleden`;}
    return 'Zojuist';
  };

  return (
    <div className="relative w-80 bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6 h-fit">
      {/* Close Button */}
      <button
        onClick={() => {
          setOpen(false);
          onClose?.();
        }}
        className="absolute top-2 right-2 opacity-70 hover:opacity-100 text-slate-400 hover:text-white transition-all"
        aria-label="Sluit AI Insights panel"
      >
        <X className="w-4 h-4" />
      </button>

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-white font-semibold">AI Insights</h3>
            <p className="text-slate-400 text-xs">Slimme meldingen & kansen</p>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-slate-400 hover:text-white transition-colors"
          aria-label={isExpanded ? "Minimaliseer insights" : "Maximaliseer insights"}
        >
          {isExpanded ? '−' : '+'}
        </button>
      </div>

      {isExpanded && (
        <>
          {/* Filter Tabs */}
          <div className="flex space-x-1 mb-4">
            {[
              { key: 'all', label: 'Alles', count: insights.length },
              { key: 'high', label: 'Hoog', count: insights.filter(i => i.priority === 'high').length },
              { key: 'medium', label: 'Medium', count: insights.filter(i => i.priority === 'medium').length },
              { key: 'low', label: 'Laag', count: insights.filter(i => i.priority === 'low').length }
            ].map((filter) => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key as any)}
                className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-700 text-slate-400 hover:text-white'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>

          {/* Insights List */}
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {filteredInsights.map((insight) => {
              const Icon = insight.icon;
              const TypeIcon = getTypeIcon(insight.type);
              
              return (
                <div
                  key={insight.id}
                  className={`p-4 rounded-lg border ${getPriorityColor(insight.priority)} transition-all hover:scale-105`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getTypeColor(insight.type)} bg-slate-700/50`}>
                      <TypeIcon className="w-4 h-4" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-white font-medium text-sm">{insight.title}</h4>
                        {insight.metric && (
                          <div className="flex items-center space-x-1">
                            <span className="text-xs font-medium text-white">
                              {insight.metric.value}
                            </span>
                            {insight.metric.trend === 'up' ? (
                              <ArrowUp className="w-3 h-3 text-green-400" />
                            ) : (
                              <ArrowDown className="w-3 h-3 text-red-400" />
                            )}
                          </div>
                        )}
                      </div>
                      
                      <p className="text-slate-300 text-xs mb-3 leading-relaxed">
                        {insight.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-slate-400 text-xs">
                          {getTimeAgo(insight.timestamp)}
                        </span>
                        
                        {insight.action && (
                          <button className="text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors">
                            {insight.action} →
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Quick Stats */}
          <div className="mt-6 pt-4 border-t border-slate-700">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">12</div>
                <div className="text-xs text-slate-400">Nieuwe leads</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">€8.5K</div>
                <div className="text-xs text-slate-400">Potentiële omzet</div>
              </div>
            </div>
          </div>

          {/* AI Status */}
          <div className="mt-4 p-3 bg-gradient-to-r from-green-900/30 to-blue-900/30 rounded-lg border border-green-500/30">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-green-400 text-xs font-medium">AI Actief</span>
              <span className="text-slate-400 text-xs">• Real-time monitoring</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
}; 