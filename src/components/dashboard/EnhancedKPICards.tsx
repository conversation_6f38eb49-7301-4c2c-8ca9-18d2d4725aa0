'use client';

import React, { useState, useEffect } from 'react';
import { getKpiStats, KpiStats } from '../../api/dashboard';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Progress } from '@/components/ui/Progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { Button } from '@/components/ui/Button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu';
import { FileText, ArrowUp, ArrowDown, DollarSign, Users, Target, MoreVertical } from 'lucide-react';
import { Skeleton } from '@/components/ui/Skeleton';

// Enhanced KPI Type Definition
interface EnhancedKPI {
  id: string;
  title: string;
  value: number;
  previousValue: number;
  trend: 'up' | 'down' | 'stable';
  target: number;
  period: string;
  icon: React.ReactNode;
  color: string;
}

// Helper to format currency
const formatCurrency = (value: number) =>
  new Intl.NumberFormat('nl-NL', { style: 'currency', currency: 'EUR' }).format(value);

// Helper to calculate trend percentage
const calculateTrend = (current: number, previous: number) => {
  if (previous === 0) {
    return current > 0 ? 100 : 0;
  }
  return ((current - previous) / previous) * 100;
};

// Trend Arrow Component
const TrendArrow: React.FC<{ trend: 'up' | 'down' | 'stable' }> = ({ trend }) => {
  if (trend === 'up') {
    return <ArrowUp className="w-4 h-4 text-green-500" />;
  }
  if (trend === 'down') {
    return <ArrowDown className="w-4 h-4 text-red-500" />;
  }
  return null;
};

export const EnhancedKPICards: React.FC = () => {
  const [kpiData, setKpiData] = useState<EnhancedKPI[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const stats: KpiStats = await getKpiStats();

        const newData: EnhancedKPI[] = [
          {
            id: 'quotes',
            title: 'Offertes',
            value: stats.totalQuotes.current,
            previousValue: stats.totalQuotes.previous,
            trend: stats.totalQuotes.current > stats.totalQuotes.previous ? 'up' : 'down',
            target: 120,
            period: 'deze maand',
            icon: <FileText className="w-6 h-6" />,
            color: 'text-blue-400',
          },
          {
            id: 'conversion',
            title: 'Conversie Ratio',
            value: stats.conversionRate.current,
            previousValue: stats.conversionRate.previous,
            trend: stats.conversionRate.current > stats.conversionRate.previous ? 'up' : 'down',
            target: 15,
            period: 'deze maand',
            icon: <Target className="w-6 h-6" />,
            color: 'text-purple-400',
          },
          {
            id: 'revenue',
            title: 'Maandelijkse Omzet',
            value: stats.revenue.current,
            previousValue: stats.revenue.previous,
            trend: stats.revenue.current > stats.revenue.previous ? 'up' : 'down',
            target: 25000,
            period: 'deze maand',
            icon: <DollarSign className="w-6 h-6" />,
            color: 'text-green-400',
          },
          {
            id: 'customers',
            title: 'Nieuwe Klanten',
            value: stats.newCustomers.current,
            previousValue: stats.newCustomers.previous,
            trend: stats.newCustomers.current > stats.newCustomers.previous ? 'up' : 'down',
            target: 20,
            period: 'deze maand',
            icon: <Users className="w-6 h-6" />,
            color: 'text-yellow-400',
          },
        ];

        setKpiData(newData);
      } catch (error) {
        console.error('Failed to fetch KPI data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="p-6">
            <CardHeader title="" className="flex flex-row items-center justify-between pb-2 space-y-0">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="w-6 h-6" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mt-2" />
              <Skeleton className="h-4 w-24 mt-2" />
              <Skeleton className="h-2 w-full mt-4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {kpiData.map((kpi) => {
          const trendPercentage = calculateTrend(kpi.value, kpi.previousValue);
          const progressValue = (kpi.value / kpi.target) * 100;

          return (
            <Card key={kpi.id} className="relative overflow-hidden border-2 border-transparent hover:border-primary/50 transition-all duration-300 ease-in-out transform hover:-translate-y-1">
              <CardHeader title={kpi.title} className="flex flex-row items-center justify-between pb-2 space-y-0">
                <h3 className="text-sm font-medium tracking-tight text-muted-foreground">{kpi.title}</h3>
                <div className={kpi.color}>{kpi.icon}</div>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline space-x-2">
                  <p className="text-3xl font-bold">
                    {kpi.id === 'revenue' ? formatCurrency(kpi.value) : kpi.id === 'conversion' ? `${kpi.value}%` : kpi.value}
                  </p>
                  <div className="flex items-center text-sm">
                    <TrendArrow trend={kpi.trend} />
                    <span className={`${kpi.trend === 'up' ? 'text-green-500' : 'text-red-500'} font-semibold`}>
                      {trendPercentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">vs. {kpi.id === 'revenue' ? formatCurrency(kpi.previousValue) : kpi.id === 'conversion' ? `${kpi.previousValue}%` : kpi.previousValue} vorige maand</p>

                <div className="mt-4">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Voortgang</span>
                    <span>Doel: {kpi.id === 'revenue' ? formatCurrency(kpi.target) : kpi.id === 'conversion' ? `${kpi.target}%` : kpi.target}</span>
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Progress value={progressValue} className="mt-1 h-2" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{`${progressValue.toFixed(0)}% van doel bereikt`}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <div className="absolute top-2 right-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="w-8 h-8">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Details</DropdownMenuItem>
                      <DropdownMenuItem>Rapport</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </TooltipProvider>
  );
};

export default EnhancedKPICards;