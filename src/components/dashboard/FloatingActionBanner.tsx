import React, { useEffect, useState } from 'react';
import { 
  UserPlus, 
  FileText, 
  Receipt, 
  Bot, 
  Upload, 
  Mail, 
  BarChart3, 
  Download,
  Search,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/stores/useAppStore';

interface FloatingAction {
  label: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
  shortcut?: string;
  badge?: number;
  description?: string;
}

export const FloatingActionBanner: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useAppStore();
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock notification counts
  const notificationCounts = {
    pendingQuotes: 3,
    newCustomers: 2,
    overdueInvoices: 1,
    aiInsights: 5
  };

  const actions: FloatingAction[] = [
    {
      label: 'Nieuwe <PERSON>lant',
      icon: <UserPlus className="w-5 h-5" />,
      color: 'bg-green-600 hover:bg-green-700',
      action: () => navigate('/customers/new'),
      shortcut: 'Ctrl+K',
      badge: notificationCounts.newCustomers,
      description: 'Voeg nieuwe klant toe'
    },
    {
      label: 'Maak Offerte', 
      icon: <FileText className="w-5 h-5" />,
      color: 'bg-blue-600 hover:bg-blue-700',
      action: () => navigate('/quotes/new'),
      shortcut: 'Ctrl+Q',
      badge: notificationCounts.pendingQuotes,
      description: 'Creëer nieuwe offerte'
    },
    {
      label: 'Nieuwe Factuur',
      icon: <Receipt className="w-5 h-5" />,
      color: 'bg-purple-600 hover:bg-purple-700', 
      action: () => navigate('/invoices/new'),
      shortcut: 'Ctrl+I',
      badge: notificationCounts.overdueInvoices,
      description: 'Genereer factuur'
    },
    {
      label: 'Innovars AI',
      icon: <Bot className="w-5 h-5" />,
      color: 'bg-gradient-to-r from-pink-500 to-violet-600 hover:from-pink-600 hover:to-violet-700',
      action: () => navigate('/innovars-ai'),
      shortcut: 'Ctrl+A',
      badge: notificationCounts.aiInsights,
      description: 'AI assistent'
    }
  ];

  const secondaryActions: FloatingAction[] = [
    {
      label: 'Import',
      icon: <Upload className="w-4 h-4" />,
      color: 'bg-indigo-600 hover:bg-indigo-700',
      action: () => showToast('Import functionaliteit', 'info'),
      description: 'CSV/Excel import'
    },
    {
      label: 'Bulk Email',
      icon: <Mail className="w-4 h-4" />,
      color: 'bg-pink-600 hover:bg-pink-700',
      action: () => showToast('Bulk email functionaliteit', 'info'),
      description: 'Verstuur naar meerdere klanten'
    },
    {
      label: 'Rapportage',
      icon: <BarChart3 className="w-4 h-4" />,
      color: 'bg-emerald-600 hover:bg-emerald-700',
      action: () => showToast('Rapportage functionaliteit', 'info'),
      description: 'Genereer maandrapport'
    },
    {
      label: 'Backup',
      icon: <Download className="w-4 h-4" />,
      color: 'bg-gray-600 hover:bg-gray-700',
      action: () => showToast('Backup functionaliteit', 'info'),
      description: 'Export alle gegevens'
    }
  ];

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Global search
      if (event.ctrlKey && event.key === '/') {
        event.preventDefault();
        setShowShortcuts(!showShortcuts);
        return;
      }

      // Action shortcuts
      if (event.ctrlKey) {
        switch (event.key.toLowerCase()) {
          case 'k':
            event.preventDefault();
            navigate('/customers/new');
            break;
          case 'q':
            event.preventDefault();
            navigate('/quotes/new');
            break;
          case 'i':
            event.preventDefault();
            navigate('/invoices/new');
            break;
          case 'a':
            event.preventDefault();
            navigate('/innovars-ai');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navigate, showShortcuts]);

  const handleActionClick = (action: FloatingAction) => {
    action.action();
    showToast(`${action.label} gestart`, 'success');
  };

  return (
    <div className="sticky top-16 z-30 bg-slate-900/95 backdrop-blur-md border-b border-white/10 shadow-lg">
      <div className="w-full px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Primary Actions */}
          <div className="flex items-center space-x-3">
            {actions.map((action, index) => (
              <div key={index} className="relative group">
                <Button
                  onClick={() => handleActionClick(action)}
                  className={cn(
                    'relative transform transition-all duration-200 hover:scale-105 hover:shadow-lg',
                    action.color,
                    'text-white font-medium px-4 py-2 rounded-lg'
                  )}
                  title={`${action.description} (${action.shortcut})`}
                >
                  {action.icon}
                  <span className="ml-2 hidden sm:inline">{action.label}</span>
                  
                  {/* Badge */}
                  {action.badge && action.badge > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {action.badge > 9 ? '9+' : action.badge}
                    </span>
                  )}
                  
                  {/* Shortcut hint */}
                  {action.shortcut && (
                    <span className="absolute -top-2 -right-2 bg-gray-700 text-xs px-1.5 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      {action.shortcut}
                    </span>
                  )}
                </Button>
              </div>
            ))}
          </div>

          {/* Search & Secondary Actions */}
          <div className="flex items-center space-x-3">
            {/* Global Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Zoeken... (Ctrl+/)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
              />
            </div>

            {/* Secondary Actions Dropdown */}
            <div className="relative group">
              <Button
                variant="outline"
                className="bg-slate-800 border-slate-700 text-gray-300 hover:text-white"
              >
                <span className="hidden sm:inline mr-2">Meer</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </Button>
              
              {/* Dropdown Menu */}
              <div className="absolute right-0 top-full mt-2 w-64 bg-slate-800 border border-slate-700 rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="p-2 space-y-1">
                  {secondaryActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => handleActionClick(action)}
                      className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-slate-700 rounded-md transition-colors"
                    >
                      {action.icon}
                      <span className="ml-3">{action.label}</span>
                      <span className="ml-auto text-xs text-gray-500">{action.description}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Notifications */}
            <Button
              variant="outline"
              className="relative bg-slate-800 border-slate-700 text-gray-300 hover:text-white"
              onClick={() => showToast('Notificaties', 'info')}
            >
              <Bell className="w-5 h-5" />
              {notificationCounts.aiInsights > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {notificationCounts.aiInsights}
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        {showShortcuts && (
          <div className="mt-3 p-3 bg-slate-800 border border-slate-700 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Nieuwe Klant:</span>
                <kbd className="px-2 py-1 bg-slate-700 rounded text-xs">Ctrl+K</kbd>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Maak Offerte:</span>
                <kbd className="px-2 py-1 bg-slate-700 rounded text-xs">Ctrl+Q</kbd>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Nieuwe Factuur:</span>
                <kbd className="px-2 py-1 bg-slate-700 rounded text-xs">Ctrl+I</kbd>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Innovars AI:</span>
                <kbd className="px-2 py-1 bg-slate-700 rounded text-xs">Ctrl+A</kbd>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}; 