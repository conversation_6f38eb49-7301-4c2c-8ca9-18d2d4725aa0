import React, { useState } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  FileText,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award
} from 'lucide-react';
import { useQuotes, useCustomers } from '@/hooks';
import { Button } from '@/components/ui/Button';
import { FormSelect } from '@/components/ui/Form';

// Mock chart data
const chartData = {
  revenue: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Omzet',
        data: [45000, 52000, 48000, 61000, 55000, 73000],
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
      },
    ],
  },
  quotes: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Offertes',
        data: [12, 19, 15, 25, 22, 30],
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
      },
    ],
  },
  conversion: {
    labels: ['Geaccepteerd', 'Afgewezen', 'Verlopen', 'Concept'],
    datasets: [
      {
        data: [65, 15, 10, 10],
        backgroundColor: ['#10B981', '#EF4444', '#F59E0B', '#6B7280'],
      },
    ],
  },
};

// KPI Cards
const KPICard: React.FC<{
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  color: string;
}> = ({ title, value, change, icon, color }) => (
  <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm text-gray-400">{title}</p>
        <p className="text-2xl font-bold text-white mt-1">{value}</p>
        <div className="flex items-center mt-2">
          {change >= 0 ? (
            <TrendingUp className="h-4 w-4 text-green-400 mr-1" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-400 mr-1" />
          )}
          <span className={`text-sm ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {change >= 0 ? '+' : ''}{change}% van vorige maand
          </span>
        </div>
      </div>
      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
        {icon}
      </div>
    </div>
  </div>
);

export const AnalyticsDashboard: React.FC = () => {
  const { quotes } = useQuotes();
  const { customers } = useCustomers();
  const [timeRange, setTimeRange] = useState('6m');

  // Calculate KPIs
  const totalRevenue = quotes.reduce((sum, quote) => sum + quote.amount, 0);
  const totalQuotes = quotes.length;
  const totalCustomers = customers.length;
  const acceptedQuotes = quotes.filter(q => q.status === 'accepted').length;
  const conversionRate = totalQuotes > 0 ? (acceptedQuotes / totalQuotes) * 100 : 0;

  // Mock data for demonstration
  const kpis = [
    {
      title: 'Totale Omzet',
      value: `€${totalRevenue.toLocaleString('nl-NL')}`,
      change: 12.5,
      icon: <DollarSign className="h-6 w-6 text-white" />,
      color: 'bg-green-500',
    },
    {
      title: 'Aantal Offertes',
      value: totalQuotes.toString(),
      change: 8.3,
      icon: <FileText className="h-6 w-6 text-white" />,
      color: 'bg-blue-500',
    },
    {
      title: 'Actieve Klanten',
      value: totalCustomers.toString(),
      change: 15.2,
      icon: <Users className="h-6 w-6 text-white" />,
      color: 'bg-purple-500',
    },
    {
      title: 'Conversie Rate',
      value: `${conversionRate.toFixed(1)}%`,
      change: 5.7,
      icon: <Target className="h-6 w-6 text-white" />,
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Analytics Dashboard</h1>
          <p className="text-gray-300">Inzicht in uw bedrijfsprestaties</p>
        </div>
        <div className="flex items-center space-x-3">
          <FormSelect
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            options={[
              { value: '1m', label: 'Laatste maand' },
              { value: '3m', label: 'Laatste 3 maanden' },
              { value: '6m', label: 'Laatste 6 maanden' },
              { value: '1y', label: 'Laatste jaar' },
            ]}
          />
          <Button variant="glass">
            <BarChart3 className="h-4 w-4 mr-2" />
            Rapport Exporteren
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi, index) => (
          <KPICard key={index} {...kpi} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Omzet Trend</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Omzet</span>
            </div>
          </div>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-400">Grafiek component wordt hier getoond</p>
              <p className="text-sm text-gray-500">Chart.js of Recharts integratie</p>
            </div>
          </div>
        </div>

        {/* Quotes Chart */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Offertes per Maand</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Offertes</span>
            </div>
          </div>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <Activity className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-400">Grafiek component wordt hier getoond</p>
              <p className="text-sm text-gray-500">Chart.js of Recharts integratie</p>
            </div>
          </div>
        </div>
      </div>

      {/* Conversion Rate Chart */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Offerte Conversie</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Geaccepteerd</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Afgewezen</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Verlopen</span>
            </div>
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-center">
            <PieChart className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-400">Pie chart component wordt hier getoond</p>
            <p className="text-sm text-gray-500">Chart.js of Recharts integratie</p>
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Customers */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Top Klanten</h3>
          <div className="space-y-3">
            {customers.slice(0, 5).map((customer, index) => (
              <div key={customer.id} className="flex items-center justify-between p-3 bg-glass-dark rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">{index + 1}</span>
                  </div>
                  <div>
                    <p className="text-white font-medium">{customer.name}</p>
                    <p className="text-sm text-gray-400">{customer.company}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">€{(Math.random() * 50000 + 10000).toLocaleString('nl-NL')}</p>
                  <p className="text-sm text-gray-400">Omzet</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Recente Activiteit</h3>
          <div className="space-y-3">
            {quotes.slice(0, 5).map((quote) => (
              <div key={quote.id} className="flex items-center space-x-3 p-3 bg-glass-dark rounded-lg">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                  <FileText className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">{quote.customerName}</p>
                  <p className="text-sm text-gray-400">{quote.projectName}</p>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">€{quote.amount.toLocaleString('nl-NL')}</p>
                  <p className="text-sm text-gray-400">{quote.status}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}; 