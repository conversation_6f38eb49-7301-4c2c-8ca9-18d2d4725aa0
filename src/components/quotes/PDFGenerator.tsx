import React from 'react';
import { 
  Document, 
  Page, 
  Text, 
  View, 
  StyleSheet, 
  PDFDownloadLink,
  Font
} from '@react-pdf/renderer';
import { Quote, QuoteItem } from '@/stores/useAppStore';

// Register fonts (you would need to add actual font files)
Font.register({
  family: 'Inter',
  src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2'
});

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Inter'
  },
  header: {
    marginBottom: 30,
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 20
  },
  companyInfo: {
    marginBottom: 10
  },
  companyName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5
  },
  companyDetails: {
    fontSize: 10,
    color: '#6b7280',
    lineHeight: 1.4
  },
  quoteInfo: {
    alignItems: 'flex-end',
    marginBottom: 20
  },
  quoteTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5
  },
  quoteNumber: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 5
  },
  quoteDate: {
    fontSize: 10,
    color: '#6b7280'
  },
  customerSection: {
    marginBottom: 30,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  customerInfo: {
    flex: 1
  },
  customerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10
  },
  customerDetails: {
    fontSize: 10,
    color: '#374151',
    lineHeight: 1.4
  },
  quoteDetails: {
    flex: 1,
    alignItems: 'flex-end'
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5
  },
  detailLabel: {
    fontSize: 10,
    color: '#6b7280'
  },
  detailValue: {
    fontSize: 10,
    color: '#374151',
    fontWeight: 'bold'
  },
  itemsSection: {
    marginBottom: 30
  },
  itemsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 15
  },
  itemsTable: {
    border: '1px solid #e5e7eb'
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f9fafb',
    borderBottom: '1px solid #e5e7eb',
    paddingVertical: 10,
    paddingHorizontal: 15
  },
  tableHeaderCell: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#374151'
  },
  tableRow: {
    flexDirection: 'row',
    borderBottom: '1px solid #e5e7eb',
    paddingVertical: 10,
    paddingHorizontal: 15
  },
  tableCell: {
    fontSize: 10,
    color: '#374151'
  },
  descriptionCell: {
    flex: 2
  },
  quantityCell: {
    flex: 1,
    textAlign: 'center'
  },
  priceCell: {
    flex: 1,
    textAlign: 'right'
  },
  totalCell: {
    flex: 1,
    textAlign: 'right',
    fontWeight: 'bold'
  },
  totalsSection: {
    marginTop: 20,
    alignItems: 'flex-end'
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    width: 200
  },
  totalLabel: {
    fontSize: 12,
    color: '#6b7280'
  },
  totalValue: {
    fontSize: 12,
    color: '#374151',
    fontWeight: 'bold'
  },
  grandTotal: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    borderTop: '1px solid #e5e7eb',
    paddingTop: 5,
    marginTop: 5
  },
  footer: {
    marginTop: 40,
    paddingTop: 20,
    borderTop: '1px solid #e5e7eb'
  },
  footerText: {
    fontSize: 9,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 1.4
  },
  description: {
    marginBottom: 30,
    padding: 15,
    backgroundColor: '#f9fafb',
    borderRadius: 5
  },
  descriptionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8
  },
  descriptionText: {
    fontSize: 10,
    color: '#374151',
    lineHeight: 1.4
  }
});

interface QuotePDFProps {
  quote: Quote;
  companyName?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
}

const QuotePDF: React.FC<QuotePDFProps> = ({ 
  quote, 
  companyName = 'AI.qoute+crm',
  companyAddress = 'Innovars Plaats 1, 9000 Gent, België',
  companyPhone = '+32 9 123 45 67',
  companyEmail = '<EMAIL>'
}) => {
  const subtotal = quote.items.reduce((sum, item) => sum + item.total, 0);
  const vat = subtotal * 0.21; // 21% VAT
  const total = subtotal + vat;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.companyInfo}>
            <Text style={styles.companyName}>{companyName}</Text>
            <Text style={styles.companyDetails}>
              {companyAddress}{'\n'}
              Tel: {companyPhone} | Email: {companyEmail}
            </Text>
          </View>
          
          <View style={styles.quoteInfo}>
            <Text style={styles.quoteTitle}>Offerte</Text>
            <Text style={styles.quoteNumber}>Nummer: {quote.number}</Text>
            <Text style={styles.quoteDate}>
              Datum: {new Date(quote.createdAt).toLocaleDateString('nl-NL')}
            </Text>
          </View>
        </View>

        {/* Customer and Quote Details */}
        <View style={styles.customerSection}>
          <View style={styles.customerInfo}>
            <Text style={styles.customerTitle}>Klant</Text>
            <Text style={styles.customerDetails}>
              {quote.customerName}{'\n'}
              Project: {quote.projectName}
            </Text>
          </View>
          
          <View style={styles.quoteDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Vervaldatum:</Text>
              <Text style={styles.detailValue}>
                {new Date(quote.expiresAt).toLocaleDateString('nl-NL')}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status:</Text>
              <Text style={styles.detailValue}>
                {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
              </Text>
            </View>
          </View>
        </View>

        {/* Items Table */}
        <View style={styles.itemsSection}>
          <Text style={styles.itemsTitle}>Project Details</Text>
          <View style={styles.itemsTable}>
            {/* Table Header */}
            <View style={styles.tableHeader}>
              <Text style={[styles.tableHeaderCell, styles.descriptionCell]}>Beschrijving</Text>
              <Text style={[styles.tableHeaderCell, styles.quantityCell]}>Aantal</Text>
              <Text style={[styles.tableHeaderCell, styles.priceCell]}>Prijs</Text>
              <Text style={[styles.tableHeaderCell, styles.totalCell]}>Totaal</Text>
            </View>
            
            {/* Table Rows */}
            {quote.items.map((item, index) => (
              <View key={index} style={styles.tableRow}>
                <Text style={[styles.tableCell, styles.descriptionCell]}>{item.description}</Text>
                <Text style={[styles.tableCell, styles.quantityCell]}>{item.quantity}</Text>
                <Text style={[styles.tableCell, styles.priceCell]}>€{item.unitPrice.toFixed(2)}</Text>
                <Text style={[styles.tableCell, styles.totalCell]}>€{item.total.toFixed(2)}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Totals */}
        <View style={styles.totalsSection}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotaal:</Text>
            <Text style={styles.totalValue}>€{subtotal.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>BTW (21%):</Text>
            <Text style={styles.totalValue}>€{vat.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={[styles.totalValue, styles.grandTotal]}>Totaal:</Text>
            <Text style={[styles.totalValue, styles.grandTotal]}>€{total.toFixed(2)}</Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Deze offerte is geldig tot {new Date(quote.expiresAt).toLocaleDateString('nl-NL')}.{'\n'}
            Voor vragen kunt u contact met ons opnemen via {companyEmail} of {companyPhone}.
          </Text>
        </View>
      </Page>
    </Document>
  );
};

interface PDFGeneratorProps {
  quote: Quote;
  companyName?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
}

export const PDFGenerator: React.FC<PDFGeneratorProps> = ({ 
  quote, 
  companyName,
  companyAddress,
  companyPhone,
  companyEmail
}) => {
  return (
    <PDFDownloadLink
      document={
        <QuotePDF 
          quote={quote}
          companyName={companyName}
          companyAddress={companyAddress}
          companyPhone={companyPhone}
          companyEmail={companyEmail}
        />
      }
      fileName={`offerte-${quote.number}.pdf`}
      className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
    >
      {({ blob, url, loading, error }) => 
        loading ? 'PDF Genereren...' : 'Download PDF'
      }
    </PDFDownloadLink>
  );
}; 