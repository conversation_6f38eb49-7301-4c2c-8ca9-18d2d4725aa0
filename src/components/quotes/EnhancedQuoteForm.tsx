import React, { useState, useRef, useCallback } from 'react';
import { 
  Mic, 
  MicOff, 
  Upload, 
  Camera, 
  Edit3, 
  Trash2, 
  Save, 
  X, 
  Plus,
  FileText,
  Euro,
  Package,
  Eye,
  Sparkles,
  Loader2
} from 'lucide-react';
import { Card, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';
import { Quote, QuoteItem } from '@/stores/useAppStore';

interface EnhancedQuoteFormProps {
  quote?: Quote;
  onSave?: (quote: Quote) => void;
  onCancel?: () => void;
  isOpen?: boolean;
  onClose?: () => void;
  mode?: 'create' | 'edit';
  onSaved?: (quote: Quote) => void;
  onError?: (error: string) => void;
}

interface AIAnalysisResult {
  materials: string[];
  description: string;
  suggestedItems: QuoteItem[];
  confidence: number;
}

export const EnhancedQuoteForm: React.FC<EnhancedQuoteFormProps> = ({
  quote,
  onSave,
  onCancel,
  isOpen,
  onClose,
  mode = 'create',
  onSaved,
  onError
}) => {
  const [formData, setFormData] = useState<Partial<Quote>>({
    number: quote?.number || `QT-${Date.now()}`,
    customerName: quote?.customerName || '',
    customerEmail: quote?.customerEmail || '',
    description: quote?.description || '',
    items: quote?.items || [],
    status: quote?.status || 'draft',
    totalAmount: quote?.totalAmount || 0,
    taxRate: quote?.taxRate || 21,
    validUntil: quote?.validUntil || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  });

  // Voice recognition state
  const [isListening, setIsListening] = useState(false);
  const [transcription, setTranscription] = useState('');
  const [voiceError, setVoiceError] = useState('');
  const recognitionRef = useRef<any>(null);

  // Photo upload state
  const [uploadedPhotos, setUploadedPhotos] = useState<File[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResult[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [loading, setLoading] = useState(false);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);

  // Initialize speech recognition
  const initializeSpeechRecognition = useCallback(() => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setVoiceError('Spraakherkenning wordt niet ondersteund in deze browser');
      return false;
    }

    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    recognitionRef.current = new SpeechRecognition();
    
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'nl-NL';

    recognitionRef.current.onstart = () => {
      setIsListening(true);
      setVoiceError('');
    };

    recognitionRef.current.onresult = (event: any) => {
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        }
      }
      if (finalTranscript) {
        setTranscription(prev => prev + ' ' + finalTranscript);
      }
    };

    recognitionRef.current.onerror = (event: any) => {
      setVoiceError(`Spraakherkenning fout: ${event.error}`);
      setIsListening(false);
    };

    recognitionRef.current.onend = () => {
      setIsListening(false);
    };

    return true;
  }, []);

  // Voice input handlers
  const startListening = () => {
    if (initializeSpeechRecognition()) {
      recognitionRef.current?.start();
    }
  };

  const stopListening = () => {
    recognitionRef.current?.stop();
  };

  const clearTranscription = () => {
    setTranscription('');
  };

  const applyTranscription = () => {
    if (transcription.trim()) {
      setFormData(prev => ({
        ...prev,
        description: prev.description + ' ' + transcription.trim()
      }));
      setTranscription('');
    }
  };

  // Photo upload handlers
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      setUploadedPhotos(prev => [...prev, ...files]);
      analyzePhotos(files);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
      setUploadedPhotos(prev => [...prev, ...files]);
      analyzePhotos(files);
    }
  };

  // AI Photo Analysis (Mock implementation)
  const analyzePhotos = async (files: File[]) => {
    setIsAnalyzing(true);
    
    // Simulate AI analysis delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const mockResults: AIAnalysisResult[] = files.map((file, index) => {
      const materials = ['Hout', 'Staal', 'Glas', 'Kunststof'];
      const descriptions = [
        'Moderne keukenrenovatie met eiken werkblad en stalen apparatuur',
        'Balkonafdichting met aluminium profielen en glazen balustrade',
        'Dakkapel constructie met houten spanten en zink dakbedekking'
      ];
      
      return {
        materials: materials.slice(0, Math.floor(Math.random() * 3) + 1),
        description: descriptions[index % descriptions.length],
        suggestedItems: [
          {
            id: `item-${Date.now()}-${index}`,
            description: descriptions[index % descriptions.length],
            quantity: Math.floor(Math.random() * 10) + 1,
            unitPrice: Math.floor(Math.random() * 500) + 100,
            unit: 'stuk'
          }
        ],
        confidence: Math.random() * 0.3 + 0.7 // 70-100% confidence
      };
    });

    setAnalysisResults(prev => [...prev, ...mockResults]);
    setIsAnalyzing(false);
  };

  // Quote item handlers
  const addQuoteItem = () => {
    const newItem: QuoteItem = {
      id: `item-${Date.now()}`,
      description: '',
      quantity: 1,
      unitPrice: 0,
      unit: 'stuk'
    };
    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  const updateQuoteItem = (id: string, field: keyof QuoteItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items?.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeQuoteItem = (id: string) => {
    if (window.confirm('Weet je zeker dat je dit item wilt verwijderen?')) {
      setFormData(prev => ({
        ...prev,
        items: prev.items?.filter(item => item.id !== id)
      }));
    }
  };

  const applyAIAnalysis = (result: AIAnalysisResult) => {
    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), ...result.suggestedItems],
      description: prev.description + ' ' + result.description
    }));
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = formData.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0;
    const taxAmount = subtotal * (formData.taxRate || 21) / 100;
    const total = subtotal + taxAmount;
    
    setFormData(prev => ({
      ...prev,
      totalAmount: total
    }));
  };

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      calculateTotals();
      const finalQuote: Quote = {
        id: quote?.id || `quote-${Date.now()}`,
        number: formData.number || '',
        customerName: formData.customerName || '',
        customerEmail: formData.customerEmail || '',
        description: formData.description || '',
        items: formData.items || [],
        status: formData.status || 'draft',
        totalAmount: formData.totalAmount || 0,
        taxRate: formData.taxRate || 21,
        validUntil: formData.validUntil || '',
        createdAt: quote?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSave?.(finalQuote);
      onSaved?.(finalQuote);
    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Onbekende fout');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900 rounded-xl border border-slate-700 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">
              {mode === 'create' ? 'Nieuwe Offerte' : 'Offerte Bewerken'}
            </h2>
            <button
              onClick={onClose}
              className="text-slate-400 hover:text-white transition-colors"
              title="Sluiten"
              aria-label="Sluiten"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Quote Details */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Offerte Details" className="pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      Offerte Nummer
                    </label>
                    <input
                      type="text"
                      value={formData.number}
                      onChange={(e) => setFormData(prev => ({ ...prev, number: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      Geldig Tot
                    </label>
                    <input
                      type="date"
                      value={formData.validUntil}
                      onChange={(e) => setFormData(prev => ({ ...prev, validUntil: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Customer Information */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Klant Informatie" className="pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      Klant Naam
                    </label>
                    <input
                      type="text"
                      value={formData.customerName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={formData.customerEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Voice Input Section */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Voice Input" className="pb-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Button
                      type="button"
                      onClick={isListening ? stopListening : startListening}
                      className={cn(
                        'flex items-center space-x-2',
                        isListening 
                          ? 'bg-red-600 hover:bg-red-700' 
                          : 'bg-blue-600 hover:bg-blue-700'
                      )}
                    >
                      {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                      <span>{isListening ? 'Stop Opname' : 'Start Voice Input'}</span>
                    </Button>
                    
                    {transcription && (
                      <>
                        <Button
                          type="button"
                          onClick={applyTranscription}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <FileText className="w-4 h-4 mr-2" />
                          Toepassen
                        </Button>
                        <Button
                          type="button"
                          onClick={clearTranscription}
                          variant="outline"
                        >
                          <X className="w-4 h-4 mr-2" />
                          Wissen
                        </Button>
                      </>
                    )}
                  </div>

                  {isListening && (
                    <div className="flex items-center space-x-2 text-blue-400">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce animate-delay-100" />
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce animate-delay-200" />
                      </div>
                      <span className="text-sm">Luisteren...</span>
                    </div>
                  )}

                  {transcription && (
                    <div className="p-3 bg-slate-800 rounded-lg border border-slate-600">
                      <p className="text-white text-sm">{transcription}</p>
                    </div>
                  )}

                  {voiceError && (
                    <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                      <p className="text-red-400 text-sm">{voiceError}</p>
                    </div>
                  )}
                </div>
              </CardHeader>
            </Card>

            {/* Photo Upload & AI Analysis */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Foto Upload & AI Analyse" className="pb-4">
                <div className="space-y-4">
                  <div
                    className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-slate-500 transition-colors cursor-pointer"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="hidden"
                    />
                    <Upload className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                    <p className="text-slate-400">Sleep foto's hierheen of klik om te selecteren</p>
                    <p className="text-slate-500 text-sm">AI analyseert automatisch materialen en genereert offerte-items</p>
                  </div>

                  {isAnalyzing && (
                    <div className="flex items-center space-x-2 text-blue-400">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>AI analyseert foto's...</span>
                    </div>
                  )}

                  {/* Uploaded Photos */}
                  {uploadedPhotos.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {uploadedPhotos.map((photo, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(photo)}
                            alt={`Upload ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                                                     <button
                             onClick={() => {
                               setUploadedPhotos(prev => prev.filter((_, i) => i !== index));
                               setAnalysisResults(prev => prev.filter((_, i) => i !== index));
                             }}
                             className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                             title="Foto verwijderen"
                             aria-label="Foto verwijderen"
                           >
                             <X className="w-3 h-3" />
                           </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* AI Analysis Results */}
                  {analysisResults.length > 0 && (
                    <div className="space-y-4">
                      <h4 className="text-white font-medium flex items-center space-x-2">
                        <Sparkles className="w-4 h-4 text-blue-400" />
                        <span>AI Analyse Resultaten</span>
                      </h4>
                      
                      {analysisResults.map((result, index) => (
                        <div key={index} className="p-4 bg-slate-800 rounded-lg border border-slate-600">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white font-medium">Foto {index + 1}</span>
                            <span className="text-green-400 text-sm">
                              {Math.round(result.confidence * 100)}% zekerheid
                            </span>
                          </div>
                          
                          <p className="text-slate-300 text-sm mb-2">{result.description}</p>
                          
                          <div className="flex flex-wrap gap-1 mb-3">
                            {result.materials.map((material, matIndex) => (
                              <span
                                key={matIndex}
                                className="px-2 py-1 bg-blue-600/20 text-blue-300 text-xs rounded"
                              >
                                {material}
                              </span>
                            ))}
                          </div>
                          
                          <Button
                            type="button"
                            onClick={() => applyAIAnalysis(result)}
                            className="bg-green-600 hover:bg-green-700 text-sm"
                          >
                            <Plus className="w-3 h-3 mr-1" />
                            Toevoegen aan Offerte
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardHeader>
            </Card>

            {/* Description */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Beschrijving" className="pb-4">
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  placeholder="Beschrijf het project of werk..."
                />
              </CardHeader>
            </Card>

            {/* Quote Items */}
            <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
              <CardHeader title="Offerte Items" className="pb-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-white font-medium">Items</h4>
                    <Button
                      type="button"
                      onClick={addQuoteItem}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Item Toevoegen
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {formData.items?.map((item, index) => (
                      <div key={item.id} className="p-4 bg-slate-800 rounded-lg border border-slate-600">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                          <div className="md:col-span-2">
                            <label className="block text-white text-sm font-medium mb-1">
                              Beschrijving
                            </label>
                            <input
                              type="text"
                              value={item.description}
                              onChange={(e) => updateQuoteItem(item.id, 'description', e.target.value)}
                              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-white text-sm font-medium mb-1">
                              Aantal
                            </label>
                            <input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updateQuoteItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-white text-sm font-medium mb-1">
                              Prijs
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => updateQuoteItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mt-3">
                          <span className="text-slate-400 text-sm">
                            Totaal: €{(item.quantity * item.unitPrice).toFixed(2)}
                          </span>
                                                     <button
                             type="button"
                             onClick={() => removeQuoteItem(item.id)}
                             className="text-red-400 hover:text-red-300 transition-colors"
                             title="Item verwijderen"
                             aria-label="Item verwijderen"
                           >
                             <Trash2 className="w-4 h-4" />
                           </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Totals */}
                  <div className="border-t border-slate-600 pt-4">
                    <div className="flex justify-between text-white">
                      <span>Subtotaal:</span>
                      <span>€{(formData.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-white">
                      <span>BTW ({formData.taxRate}%):</span>
                      <span>€{((formData.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0) * (formData.taxRate || 21) / 100).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-white font-bold text-lg border-t border-slate-600 pt-2">
                      <span>Totaal:</span>
                      <span>€{formData.totalAmount?.toFixed(2) || '0.00'}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4">
              <Button
                type="button"
                onClick={onCancel}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:text-white"
              >
                Annuleren
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Opslaan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {mode === 'create' ? 'Offerte Aanmaken' : 'Offerte Bijwerken'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}; 