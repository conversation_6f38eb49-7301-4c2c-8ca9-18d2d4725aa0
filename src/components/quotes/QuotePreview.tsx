import React from 'react';
import { Calendar, User, Building, FileText, Euro, Clock } from 'lucide-react';
import { Quote, QuoteItem, Customer } from '@/stores/useAppStore';

interface QuotePreviewProps {
  formData: {
    customerId: string;
    customerName: string;
    projectName: string;
    items: QuoteItem[];
    expiresAt: string;
    description?: string;
  };
  customerData?: Customer;
  isLoading?: boolean;
}

export const QuotePreview: React.FC<QuotePreviewProps> = ({
  formData,
  customerData,
  isLoading = false
}) => {
  // Calculate totals
  const subtotal = formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const btw = subtotal * 0.21;
  const total = subtotal + btw;

  // Generate quote number
  const quoteNumber = `QT-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 h-fit sticky top-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded mb-6"></div>
          <div className="h-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 h-fit sticky top-6 max-h-[calc(100vh-2rem)] overflow-y-auto">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Innovars</h1>
              <p className="text-sm text-gray-500">Intelligente Offerte Generatie</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Quote Nummer</p>
            <p className="font-mono text-sm font-semibold text-gray-900">{quoteNumber}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <div>
              <p className="text-gray-500">Aanmaakdatum</p>
              <p className="font-medium text-gray-900">{new Date().toLocaleDateString('nl-NL')}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <div>
              <p className="text-gray-500">Vervaldatum</p>
              <p className="font-medium text-gray-900">
                {formData.expiresAt ? new Date(formData.expiresAt).toLocaleDateString('nl-NL') : 'Niet ingesteld'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
          <User className="w-5 h-5 mr-2 text-blue-600" />
          Klant Informatie
        </h2>
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Building className="w-4 h-4 text-gray-400" />
            <p className="font-medium text-gray-900">
              {formData.customerName || 'Klant niet geselecteerd'}
            </p>
          </div>
          {customerData && (
            <div className="text-sm text-gray-600 space-y-1">
              {customerData.email && <p>{customerData.email}</p>}
              {customerData.phone && <p>{customerData.phone}</p>}
              {customerData.address && <p>{customerData.address}</p>}
            </div>
          )}
        </div>
      </div>

      {/* Project Information */}
      {formData.projectName && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Project</h2>
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="font-medium text-gray-900">{formData.projectName}</p>
            {formData.description && (
              <p className="text-sm text-gray-600 mt-2">{formData.description}</p>
            )}
          </div>
        </div>
      )}

      {/* Quote Items */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Offerte Items</h2>
        {formData.items.length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <p className="text-gray-500">Nog geen items toegevoegd</p>
          </div>
        ) : (
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Beschrijving
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aantal
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prijs
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Totaal
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {formData.items.map((item, index) => (
                  <tr key={index}>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {item.description || 'Geen beschrijving'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 text-right">
                      {item.quantity}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 text-right">
                      €{item.unitPrice?.toFixed(2) || '0.00'}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900 text-right">
                      €{((item.quantity || 0) * (item.unitPrice || 0)).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Totals */}
      <div className="border-t border-gray-200 pt-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Subtotaal:</span>
            <span className="font-medium text-gray-900">€{subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">BTW (21%):</span>
            <span className="font-medium text-gray-900">€{btw.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
            <span className="text-gray-900">Totaal:</span>
            <span className="text-blue-600">€{total.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 space-y-1">
          <p>Innovars - Intelligente Offerte Generatie & CRM</p>
          <p>Voor vragen: <EMAIL></p>
          <p>Algemene voorwaarden zijn van toepassing</p>
        </div>
      </div>
    </div>
  );
}; 