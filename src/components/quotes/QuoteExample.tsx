import React, { useState } from 'react';
import { QuoteList } from './QuoteList';
import { QuoteForm } from './QuoteForm';
import { VoiceInput } from '../rita/VoiceInput';
import { Quote } from '@/stores/useAppStore';

export const QuoteExample: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingQuote, setEditingQuote] = useState<Quote | undefined>();
  const [voiceTranscript, setVoiceTranscript] = useState('');

  const handleCreateQuote = () => {
    setEditingQuote(undefined);
    setShowForm(true);
  };

  const handleEditQuote = (quote: Quote) => {
    setEditingQuote(quote);
    setShowForm(true);
  };

  const handleSaveQuote = (quote: Quote) => {
    console.log('Quote saved:', quote);
    setShowForm(false);
    setEditingQuote(undefined);
  };

  const handleCancelQuote = () => {
    setShowForm(false);
    setEditingQuote(undefined);
  };

  const handleVoiceInput = (text: string) => {
    setVoiceTranscript(text);
    console.log('Voice input:', text);
  };

  if (showForm) {
    return (
      <div className="space-y-6">
        <QuoteForm
          quote={editingQuote}
          onSave={handleSaveQuote}
          onCancel={handleCancelQuote}
        />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Voice Input Demo */}
      <div className="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Voice Input Demo</h2>
        <VoiceInput
          onTranscript={setVoiceTranscript}
          onSend={handleVoiceInput}
          placeholder="Spreek je quote details..."
          autoSend={false}
        />
        {voiceTranscript && (
          <div className="mt-4 p-3 bg-white/5 rounded-lg">
            <p className="text-white text-sm">
              <strong>Transcript:</strong> {voiceTranscript}
            </p>
          </div>
        )}
      </div>

      {/* Quote List */}
      <div>
        <QuoteList />
      </div>
    </div>
  );
}; 