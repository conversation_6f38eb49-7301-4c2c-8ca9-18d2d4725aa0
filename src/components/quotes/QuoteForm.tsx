import React, { useState, useEffect, useRef } from 'react';
import { 
  Plus, 
  Trash2, 
  Mic, 
  MicOff, 
  Save, 
  Send, 
  Download,
  Calculator,
  User,
  Calendar,
  Euro,
  Package,
  Eye,
  Upload,
  Camera,
  Edit3,
  X,
  Check,
  AlertCircle
} from 'lucide-react';
import { <PERSON>, CardHeader, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Select, SelectOption } from '@/components/ui/Select';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { quoteApi, CreateQuoteRequest, UpdateQuoteRequest } from '@/api/quotes';
import { Quote, QuoteItem } from '@/stores/useAppStore';
import { cn } from '@/utils/cn';
import { QuotePreview } from './QuotePreview';
import { useVoiceRecognition } from '@/hooks/useVoiceRecognition';
import { useImageAnalysis } from '@/hooks/useImageAnalysis';

// Customer options (mock data - later from API)
const customerOptions: SelectOption[] = [
  { value: 'customer-1', label: '<PERSON>', icon: <User size={16} /> },
  { value: 'customer-2', label: 'Bouwbedrijf Visser', icon: <User size={16} /> },
  { value: 'customer-3', label: 'Woningbouw BV', icon: <User size={16} /> },
];

// Service options
const serviceOptions: SelectOption[] = [
  { value: 'renovation', label: 'Renovatie' },
  { value: 'construction', label: 'Bouw' },
  { value: 'maintenance', label: 'Onderhoud' },
  { value: 'design', label: 'Ontwerp' },
  { value: 'consultation', label: 'Advies' },
];

interface QuoteFormProps {
  quote?: Quote; // Voor edit mode
  onSave?: (quote: Quote) => void;
  onCancel?: () => void;
  // Modal props
  isOpen?: boolean;
  onClose?: () => void;
  mode?: 'create' | 'edit';
  onSaved?: (quote: Quote) => void;
  onError?: (error: string) => void;
}

export const QuoteForm: React.FC<QuoteFormProps> = ({ 
  quote, 
  onSave, 
  onCancel,
  isOpen,
  onClose,
  mode = 'create',
  onSaved,
  onError
}) => {
  const isEditMode = !!quote;
  
  // Form state
  const [formData, setFormData] = useState({
    customerId: quote?.customerId || '',
    customerName: quote?.customerName || '',
    projectName: quote?.projectName || '',
    expiresAt: quote?.expiresAt ? new Date(quote.expiresAt).toISOString().split('T')[0] : '',
    items: quote?.items || [],
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingText, setRecordingText] = useState('');
  
  // Voice recognition hook
  const {
    isListening,
    transcript,
    isSupported: voiceSupported,
    error: voiceError,
    startListening,
    stopListening,
    resetTranscript,
    setTranscript
  } = useVoiceRecognition();

  // Image analysis hook
  const {
    isAnalyzing,
    results: imageResults,
    error: imageError,
    uploadImage,
    resetResults: resetImageResults
  } = useImageAnalysis();

  // File input ref
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle voice recognition
  const handleVoiceToggle = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadImage(file);
    }
  };

  // Add AI-generated items to quote
  const addAIGeneratedItems = () => {
    if (imageResults?.suggestedItems) {
      setFormData(prev => ({
        ...prev,
        items: [...prev.items, ...imageResults.suggestedItems]
      }));
      resetImageResults();
    }
  };

  // Handle form field changes
  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle customer selection
  const handleCustomerChange = (value: string | string[]) => {
    const customerId = Array.isArray(value) ? value[0] : value;
    const selectedCustomer = customerOptions.find(c => c.value === customerId);
    
    setFormData(prev => ({
      ...prev,
      customerId,
      customerName: selectedCustomer?.label || '',
    }));
  };

  // Add new item
  const addItem = () => {
    const newItem: Omit<QuoteItem, 'id'> = {
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { ...newItem, id: `temp-${Date.now()}` }],
    }));
  };

  // Update item
  const updateItem = (index: number, field: keyof QuoteItem, value: string | number) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      const item = { ...newItems[index] };
      
      if (field === 'quantity' || field === 'unitPrice') {
        const numValue = typeof value === 'string' ? parseFloat(value) || 0 : value;
        item[field] = numValue;
        item.total = item.quantity * item.unitPrice;
      } else {
        item[field] = value as string;
      }
      
      newItems[index] = item;
      return { ...prev, items: newItems };
    });
  };

  // Remove item
  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  // Calculate totals
  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
  const btw = subtotal * 0.21; // 21% BTW
  const total = subtotal + btw;

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.customerId || !formData.projectName || formData.items.length === 0) {
      setError('Vul alle verplichte velden in');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const quoteData = {
        customerId: formData.customerId,
        customerName: formData.customerName,
        projectName: formData.projectName,
        items: formData.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
        expiresAt: new Date(formData.expiresAt),
      };

      let result;
      if (isEditMode && quote) {
        result = await quoteApi.updateQuote(quote.id, quoteData as UpdateQuoteRequest);
      } else {
        result = await quoteApi.createQuote(quoteData as CreateQuoteRequest);
      }

      onSave?.(result.data);
    } catch (err) {
      setError('Fout bij het opslaan van quote');
      console.error('Error saving quote:', err);
    } finally {
      setLoading(false);
    }
  };

  // Start/stop voice recording
  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // TODO: Process recordingText and add to form
    } else {
      // Start recording
      setIsRecording(true);
      setRecordingText('');
    }
  };

  // Mock customer data for preview
  const mockCustomerData = {
    id: formData.customerId,
    name: formData.customerName,
    email: '<EMAIL>',
    phone: '+31 6 12345678',
    address: 'Hoofdstraat 123, 1234 AB Amsterdam',
    createdAt: new Date().toISOString(),
    status: 'active'
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">
            {isEditMode ? 'Quote Bewerken' : 'Nieuwe Quote'}
          </h1>
          <p className="text-white/60">
            {isEditMode ? 'Bewerk de quote details' : 'Maak een nieuwe quote aan'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Voice Recognition Button */}
          <Button
            variant="outline"
            onClick={handleVoiceToggle}
            disabled={!voiceSupported}
            className={cn(
              isListening && 'bg-red-500/20 text-red-300 border-red-500/30 animate-pulse'
            )}
            title={voiceSupported ? "Spraakherkenning" : "Spraakherkenning niet ondersteund"}
            aria-label={isListening ? "Stop spraakherkenning" : "Start spraakherkenning"}
          >
            {isListening ? <MicOff size={16} /> : <Mic size={16} />}
            {isListening ? 'Stop' : 'Voice'}
          </Button>

          {/* Image Upload Button */}
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isAnalyzing}
            className={cn(
              isAnalyzing && 'bg-blue-500/20 text-blue-300 border-blue-500/30'
            )}
            title="Foto uploaden voor AI analyse"
            aria-label="Foto uploaden"
          >
            {isAnalyzing ? <LoadingSpinner size="sm" /> : <Camera size={16} />}
            {isAnalyzing ? 'Analyseren...' : 'Foto'}
          </Button>
          
          <Button
            variant="outline"
            onClick={onCancel}
          >
            Annuleren
          </Button>
          
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="bg-primary-500 hover:bg-primary-600"
          >
            {loading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <Save size={16} />
            )}
            {isEditMode ? 'Bijwerken' : 'Opslaan'}
          </Button>
        </div>
      </div>

      {/* Voice Recording Indicator */}
      {isListening && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Mic size={16} className="text-red-400 animate-pulse" />
            <span className="text-red-300 font-medium">Spraakherkenning actief</span>
          </div>
          <p className="text-red-200 text-sm">{transcript || 'Spreek nu...'}</p>
          <div className="flex items-center gap-2 mt-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setTranscript('')}
              className="text-red-300 border-red-300 hover:bg-red-500/20"
            >
              <X size={14} />
              Wissen
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                // Add transcript to project description
                setFormData(prev => ({
                  ...prev,
                  projectName: prev.projectName + ' ' + transcript
                }));
                setTranscript('');
              }}
              className="text-green-300 border-green-300 hover:bg-green-500/20"
            >
              <Check size={14} />
              Toevoegen
            </Button>
          </div>
        </div>
      )}

      {/* Voice Error */}
      {voiceError && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle size={16} className="text-red-400" />
            <p className="text-red-300">{voiceError}</p>
          </div>
        </div>
      )}

      {/* Image Analysis Results */}
      {imageResults && (
        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Camera size={16} className="text-blue-400" />
              <span className="text-blue-300 font-medium">AI Analyse Resultaat</span>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={resetImageResults}
              className="text-blue-300 border-blue-300 hover:bg-blue-500/20"
            >
              <X size={14} />
            </Button>
          </div>
          
          <div className="space-y-3">
            <div>
              <p className="text-blue-200 font-medium">{imageResults.description}</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {imageResults.materials.map((material, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-500/20 text-blue-200 text-xs rounded">
                    {material}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={addAIGeneratedItems}
                className="text-green-300 border-green-300 hover:bg-green-500/20"
              >
                <Plus size={14} />
                Items Toevoegen ({imageResults.suggestedItems.length})
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Image Analysis Error */}
      {imageError && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle size={16} className="text-red-400" />
            <p className="text-red-300">{imageError}</p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-300">{error}</p>
        </div>
      )}

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        {/* Quote Form - 3 columns */}
        <div className="xl:col-span-3">
          <form onSubmit={handleSubmit} className="space-y-6">
        {/* Quote Details */}
        <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
          <CardHeader title="Quote Details" className="pb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <label className="text-white text-sm font-medium">Klant *</label>
                <Select
                  value={formData.customerId}
                  onChange={handleCustomerChange}
                  options={customerOptions}
                  placeholder="Selecteer klant"
                  searchable
                  clearable
                />
              </div>

              {/* Project Name */}
              <div className="space-y-2">
                <label className="text-white text-sm font-medium">Project Naam *</label>
                <input
                  type="text"
                  value={formData.projectName}
                  onChange={(e) => handleFieldChange('projectName', e.target.value)}
                  placeholder="Project naam"
                  className="w-full px-3 py-2 bg-transparent border border-white/20 rounded-md text-white placeholder-white/60 focus:outline-none focus:border-primary-500"
                />
              </div>

              {/* Expiry Date */}
              <div className="space-y-2">
                <label className="text-white text-sm font-medium">Vervaldatum *</label>
                <input
                  type="date"
                  value={formData.expiresAt}
                  onChange={(e) => handleFieldChange('expiresAt', e.target.value)}
                  className="w-full px-3 py-2 bg-transparent border border-white/20 rounded-md text-white focus:outline-none focus:border-primary-500"
                />
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Quote Items */}
        <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
          <CardHeader title="Quote Items" className="pb-4">
            <div className="space-y-4">
              {/* Items List */}
              {formData.items.map((item, index) => (
                <div key={item.id} className="grid grid-cols-12 gap-2 items-center p-3 bg-white/5 rounded-lg">
                  {/* Description */}
                  <div className="col-span-6">
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => updateItem(index, 'description', e.target.value)}
                      placeholder="Beschrijving"
                      className="w-full px-2 py-1 bg-transparent border border-white/20 rounded text-white placeholder-white/60 focus:outline-none focus:border-primary-500 text-sm"
                    />
                  </div>

                  {/* Quantity */}
                  <div className="col-span-2">
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                      placeholder="Aantal"
                      min="0"
                      step="0.01"
                      className="w-full px-2 py-1 bg-transparent border border-white/20 rounded text-white focus:outline-none focus:border-primary-500 text-sm"
                    />
                  </div>

                  {/* Unit Price */}
                  <div className="col-span-2">
                    <input
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                      placeholder="€ Prijs"
                      min="0"
                      step="0.01"
                      className="w-full px-2 py-1 bg-transparent border border-white/20 rounded text-white focus:outline-none focus:border-primary-500 text-sm"
                    />
                  </div>

                  {/* Total */}
                  <div className="col-span-1 text-right">
                    <span className="text-white font-medium text-sm">
                      €{item.total.toFixed(2)}
                    </span>
                  </div>

                  {/* Remove Button */}
                  <div className="col-span-1 text-right">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(index)}
                      className="text-red-400 hover:text-red-300 p-1"
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              ))}

              {/* Add Item Button */}
              <Button
                type="button"
                variant="outline"
                onClick={addItem}
                className="w-full"
              >
                <Plus size={16} className="mr-2" />
                Item Toevoegen
              </Button>
            </div>
          </CardHeader>
        </Card>

        {/* Totals */}
        <Card className="bg-glass-dark backdrop-blur-sm border border-white/20">
          <CardHeader title="Totaal" className="pb-4">
            <div className="space-y-2">
              <div className="flex justify-between text-white/60">
                <span>Subtotaal:</span>
                <span>€{subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-white/60">
                <span>BTW (21%):</span>
                <span>€{btw.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-white font-bold text-lg border-t border-white/20 pt-2">
                <span>Totaal:</span>
                <span>€{total.toFixed(2)}</span>
              </div>
            </div>
          </CardHeader>
        </Card>
          </form>
        </div>

        {/* Quote Preview - 2 columns */}
        <div className="xl:col-span-2">
          <QuotePreview 
            formData={formData}
            customerData={mockCustomerData}
            isLoading={loading}
          />
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
        aria-label="Foto uploaden"
      />

      {/* Mobile Preview Toggle */}
      <div className="xl:hidden">
        <Button
          variant="outline"
          onClick={() => {/* TODO: Toggle mobile preview */}}
          className="w-full"
        >
          <Eye size={16} className="mr-2" />
          Preview Bekijken
        </Button>
      </div>
    </div>
  );
}; 