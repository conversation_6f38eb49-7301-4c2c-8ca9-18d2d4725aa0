import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { FormInput, FormSelect } from '@/components/ui/Form';
import { Modal } from '@/components/ui/Modal';
import { useToast } from '@/contexts/ToastContext';
import { QuoteItem } from '@/stores/useAppStore';
import {
    Building,
    Copy,
    Edit,
    Eye,
    FileText,
    Home,
    Palette,
    Plus,
    Search,
    Star,
    StarOff,
    Trash2,
    Wrench,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';

export interface QuoteTemplate {
  id: string;
  name: string;
  description: string;
  category: 'bathroom' | 'kitchen' | 'renovation' | 'electrical' | 'plumbing' | 'painting' | 'general';
  items: QuoteItem[];
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
  estimatedDuration: string; // e.g., "2-3 weken"
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
}

const categoryIcons = {
  bathroom: Building,
  kitchen: Home,
  renovation: Wrench,
  electrical: Zap,
  plumbing: Wrench,
  painting: Palette,
  general: FileText,
};

const categoryLabels = {
  bathroom: 'Badkamer',
  kitchen: 'Keuken',
  renovation: 'Renovatie',
  electrical: 'Elektra',
  plumbing: 'Loodgieterij',
  painting: 'Schilderwerk',
  general: 'Algemeen',
};

const difficultyLabels = {
  easy: 'Eenvoudig',
  medium: 'Gemiddeld',
  hard: 'Complex',
};

const difficultyColors = {
  easy: 'text-green-400',
  medium: 'text-yellow-400',
  hard: 'text-red-400',
};

// Mock templates data
const mockTemplates: QuoteTemplate[] = [
  {
    id: '1',
    name: 'Standaard Badkamer Renovatie',
    description: 'Complete badkamer renovatie inclusief tegels, sanitair en leidingwerk',
    category: 'bathroom',
    items: [
      { id: '1', description: 'Tegels verwijderen en plaatsen', quantity: 1, unitPrice: 2500, total: 2500 },
      { id: '2', description: 'Sanitair installatie', quantity: 1, unitPrice: 1800, total: 1800 },
      { id: '3', description: 'Leidingwerk aanpassen', quantity: 1, unitPrice: 1200, total: 1200 },
    ],
    isFavorite: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
    usageCount: 15,
    estimatedDuration: '2-3 weken',
    difficulty: 'medium',
    tags: ['badkamer', 'renovatie', 'tegels', 'sanitair'],
  },
  {
    id: '2',
    name: 'Keuken Upgrade Basis',
    description: 'Basis keuken upgrade met nieuwe kasten en werkblad',
    category: 'kitchen',
    items: [
      { id: '1', description: 'Keukenkasten plaatsen', quantity: 1, unitPrice: 3500, total: 3500 },
      { id: '2', description: 'Werkblad installatie', quantity: 1, unitPrice: 800, total: 800 },
      { id: '3', description: 'Afwerking en montage', quantity: 1, unitPrice: 600, total: 600 },
    ],
    isFavorite: false,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    usageCount: 8,
    estimatedDuration: '1-2 weken',
    difficulty: 'easy',
    tags: ['keuken', 'kasten', 'werkblad'],
  },
];

interface QuoteTemplatesProps {
  onSelectTemplate: (template: QuoteTemplate) => void;
}

export const QuoteTemplates: React.FC<QuoteTemplatesProps> = ({ onSelectTemplate }) => {
  const { showToast } = useToast();
  const [templates, setTemplates] = useState<QuoteTemplate[]>(mockTemplates);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<QuoteTemplate | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<QuoteTemplate | null>(null);

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = !selectedCategory || template.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const toggleFavorite = (templateId: string) => {
    setTemplates(prev =>
      prev.map(template =>
        template.id === templateId
          ? { ...template, isFavorite: !template.isFavorite }
          : template
      )
    );
  };

  const duplicateTemplate = (template: QuoteTemplate) => {
    const newTemplate: QuoteTemplate = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (Kopie)`,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
    };

    setTemplates(prev => [newTemplate, ...prev]);
    showToast({
      type: 'success',
      title: 'Template Gekopieerd',
      message: `Template "${template.name}" is succesvol gekopieerd.`,
    });
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(template => template.id !== templateId));
    showToast({
      type: 'success',
      title: 'Template Verwijderd',
      message: 'Template is succesvol verwijderd.',
    });
  };

  const useTemplate = (template: QuoteTemplate) => {
    // Update usage count
    setTemplates(prev =>
      prev.map(t =>
        t.id === template.id
          ? { ...t, usageCount: t.usageCount + 1 }
          : t
      )
    );

    onSelectTemplate(template);
    showToast({
      type: 'success',
      title: 'Template Toegepast',
      message: `Template "${template.name}" is toegepast op de offerte.`,
    });
  };

  const handlePreviewTemplate = (template: QuoteTemplate) => {
    setPreviewTemplate(template);
    setShowPreviewModal(true);
  };

  const totalAmount = (template: QuoteTemplate) => {
    return template.items.reduce((sum, item) => sum + item.total, 0);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Quote Templates</h2>
          <p className="text-gray-300">Herbruikbare templates voor snelle offerte generatie</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nieuwe Template
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <FormInput
            placeholder="Zoek templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <FormSelect
          placeholder="Alle categorieën"
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          options={[
            { value: '', label: 'Alle categorieën' },
            ...Object.entries(categoryLabels).map(([value, label]) => ({ value, label })),
          ]}
        />
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => {
          const IconComponent = categoryIcons[template.category];

          return (
            <Card key={template.id} className="bg-glass-light backdrop-blur-md border border-white/20 p-6 hover:border-white/40 transition-colors">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white">{template.name}</h3>
                      <p className="text-sm text-gray-400">{categoryLabels[template.category]}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleFavorite(template.id)}
                    className="text-gray-400 hover:text-yellow-400 transition-colors"
                  >
                    {template.isFavorite ? (
                      <Star className="h-5 w-5 fill-current text-yellow-400" />
                    ) : (
                      <StarOff className="h-5 w-5" />
                    )}
                  </button>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-300 line-clamp-2">{template.description}</p>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">Geschatte prijs</p>
                    <p className="font-semibold text-white">€{totalAmount(template).toLocaleString('nl-NL')}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Duur</p>
                    <p className="font-semibold text-white">{template.estimatedDuration}</p>
                  </div>
                </div>

                {/* Difficulty and Usage */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400">Complexiteit:</span>
                    <span className={`font-medium ${difficultyColors[template.difficulty]}`}>
                      {difficultyLabels[template.difficulty]}
                    </span>
                  </div>
                  <span className="text-gray-400">{template.usageCount}x gebruikt</span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="inline-block bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="inline-block text-gray-400 px-2 py-1 text-xs">
                      +{template.tags.length - 3} meer
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 pt-2 border-t border-white/20">
                  <Button
                    size="sm"
                    onClick={() => useTemplate(template)}
                    className="flex-1"
                  >
                    Gebruiken
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePreviewTemplate(template)}
                    title="Voorvertoning"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => duplicateTemplate(template)}
                    title="Dupliceren"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingTemplate(template)}
                    title="Bewerken"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteTemplate(template.id)}
                    className="text-red-400 hover:text-red-300"
                    title="Verwijderen"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Geen templates gevonden</h3>
          <p className="text-gray-400 mb-4">
            {searchQuery || selectedCategory
              ? 'Probeer uw zoekfilters aan te passen'
              : 'Maak uw eerste template om snel offertes te genereren'
            }
          </p>
          {!searchQuery && !selectedCategory && (
            <Button onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Eerste Template Maken
            </Button>
          )}
        </div>
      )}

      {/* Preview Modal */}
      <Modal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        title={`Template: ${previewTemplate?.name}`}
        size="lg"
      >
        {previewTemplate && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-400">Categorie</p>
                <p className="text-white">{categoryLabels[previewTemplate.category]}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Geschatte duur</p>
                <p className="text-white">{previewTemplate.estimatedDuration}</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-400 mb-2">Beschrijving</p>
              <p className="text-white">{previewTemplate.description}</p>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Items</h4>
              <div className="space-y-2">
                {previewTemplate.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center p-3 bg-glass-dark rounded-lg">
                    <div>
                      <p className="text-white font-medium">{item.description}</p>
                      <p className="text-sm text-gray-400">{item.quantity}x à €{item.unitPrice}</p>
                    </div>
                    <p className="text-white font-semibold">€{item.total.toLocaleString('nl-NL')}</p>
                  </div>
                ))}
              </div>
              <div className="border-t border-white/20 pt-4 mt-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-white">Totaal</span>
                  <span className="text-xl font-bold text-blue-400">
                    €{totalAmount(previewTemplate).toLocaleString('nl-NL')}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="ghost"
                onClick={() => setShowPreviewModal(false)}
              >
                Sluiten
              </Button>
              <Button
                onClick={() => {
                  useTemplate(previewTemplate);
                  setShowPreviewModal(false);
                }}
              >
                Template Gebruiken
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};
