import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Download, 
  Mail, 
  Copy, 
  Trash2, 
  Eye,
  Calendar,
  User,
  Euro,
  Clock
} from 'lucide-react';
import { Card, CardHeader, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Select, SelectOption } from '@/components/ui/Select';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { quoteApi, QuoteFilters } from '@/api/quotes';
import { Quote } from '@/stores/useAppStore';
import { cn } from '@/utils/cn';

// Status options voor filter
const statusOptions: SelectOption[] = [
  { value: 'all', label: 'Alle statussen' },
  { value: 'draft', label: 'Concept' },
  { value: 'sent', label: 'Verzonden' },
  { value: 'accepted', label: 'Geaccepteerd' },
  { value: 'rejected', label: 'Afgewezen' },
  { value: 'expired', label: 'Verlopen' },
];

// Quote status badges
const getStatusBadge = (status: Quote['status']) => {
  const statusConfig = {
    draft: { label: 'Concept', className: 'bg-gray-500/20 text-gray-300' },
    sent: { label: 'Verzonden', className: 'bg-blue-500/20 text-blue-300' },
    accepted: { label: 'Geaccepteerd', className: 'bg-green-500/20 text-green-300' },
    rejected: { label: 'Afgewezen', className: 'bg-red-500/20 text-red-300' },
    expired: { label: 'Verlopen', className: 'bg-orange-500/20 text-orange-300' },
  };

  const config = statusConfig[status];
  return (
    <span className={cn(
      'px-2 py-1 rounded-full text-xs font-medium',
      config.className
    )}>
      {config.label}
    </span>
  );
};

// Quote card component
const QuoteCard: React.FC<{ 
  quote: Quote; 
  onEdit: (id: string) => void; 
  onDelete: (id: string) => void;
  onPreview: (quote: Quote) => void;
}> = ({ 
  quote, 
  onEdit, 
  onDelete,
  onPreview
}) => {
  const totalAmount = quote.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const isExpired = new Date(quote.expiresAt) < new Date();

  return (
    <Card className="bg-glass-dark backdrop-blur-sm border border-white/20 hover:border-white/30 transition-all duration-200">
      <CardHeader title="" className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-white font-semibold truncate">{quote.projectName}</h3>
            <p className="text-white/60 text-sm truncate">{quote.customerName}</p>
          </div>
          <div className="flex items-center gap-1">
            {getStatusBadge(quote.status)}
            {isExpired && (
              <Clock size={14} className="text-orange-400" />
            )}
          </div>
        </div>
      </CardHeader>

      <div className="px-4 pb-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-1 text-white/60">
            <Calendar size={14} />
            <span>Vervalt: {new Date(quote.expiresAt).toLocaleDateString('nl-NL')}</span>
          </div>
          <div className="flex items-center gap-1 text-white font-medium">
            <Euro size={14} />
            <span>€{totalAmount.toFixed(2)}</span>
          </div>
        </div>
        
        <div className="mt-2 text-xs text-white/40">
          {quote.items.length} items • Aangemaakt {new Date(quote.createdAt).toLocaleDateString('nl-NL')}
        </div>
      </div>

      <CardFooter className="pt-0">
        <div className="flex items-center gap-1 w-full">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onPreview(quote)}
            className="flex-1"
            title="Preview offerte"
            aria-label="Preview offerte"
          >
            <Eye size={14} className="mr-1" />
            Preview
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {/* TODO: Download PDF */}}
            className="flex-1"
          >
            <Download size={14} className="mr-1" />
            PDF
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {/* TODO: Send email */}}
            className="flex-1"
          >
            <Mail size={14} className="mr-1" />
            Verstuur
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {/* TODO: Duplicate */}}
            className="flex-1"
          >
            <Copy size={14} className="mr-1" />
            Kopieer
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onDelete(quote.id)}
            className="text-red-400 hover:text-red-300"
          >
            <Trash2 size={14} />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

// Main QuoteList component
interface QuoteListProps {
  onCreateQuote?: () => void;
  onEditQuote?: (quote: Quote) => void;
  onPreviewQuote?: (quote: Quote) => void;
}

export const QuoteList: React.FC<QuoteListProps> = ({ 
  onCreateQuote, 
  onEditQuote,
  onPreviewQuote 
}) => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<QuoteFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  // Load quotes
  const loadQuotes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await quoteApi.getQuotes({
        ...filters,
        search: searchTerm || undefined,
      });
      
      setQuotes(response.data || []);
    } catch (err) {
      setError('Fout bij het laden van quotes');
      console.error('Error loading quotes:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load quotes on mount and filter changes
  useEffect(() => {
    loadQuotes();
  }, [filters, searchTerm]);

  // Handle quote deletion
  const handleDeleteQuote = async (id: string) => {
    if (!confirm('Weet je zeker dat je deze quote wilt verwijderen?')) {return;}
    
    try {
      await quoteApi.deleteQuote(id);
      setQuotes(quotes.filter(q => q.id !== id));
    } catch (err) {
      setError('Fout bij het verwijderen van quote');
      console.error('Error deleting quote:', err);
    }
  };

  // Handle quote edit
  const handleEditQuote = (id: string) => {
    // TODO: Navigate to quote edit page
    console.log('Edit quote:', id);
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string | string[]) => {
    const statusValue = Array.isArray(value) ? value[0] : value;
    setFilters(prev => ({
      ...prev,
      status: statusValue === 'all' ? undefined : statusValue as Quote['status'],
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400 mb-4">{error}</p>
        <Button onClick={loadQuotes}>Opnieuw proberen</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header met filters */}
      <div className="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-white mb-2">Quotes</h1>
            <p className="text-white/60">
              {quotes.length} quote{quotes.length !== 1 ? 's' : ''} gevonden
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'table' : 'grid')}
            >
              {viewMode === 'grid' ? 'Tabel' : 'Grid'}
            </Button>
            
            <Button
              onClick={() => {/* TODO: Navigate to create quote */}}
              className="bg-primary-500 hover:bg-primary-600"
            >
              <Plus size={16} className="mr-2" />
              Nieuwe Quote
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-3 mt-4">
          <div className="flex-1 relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
            <input
              type="text"
              placeholder="Zoek in quotes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-transparent border border-white/20 rounded-md text-white placeholder-white/60 focus:outline-none focus:border-primary-500"
            />
          </div>
          
          <Select
            value={filters.status || 'all'}
            onChange={handleStatusFilterChange}
            options={statusOptions}
            placeholder="Filter op status"
            size="sm"
          />
        </div>
      </div>

      {/* Quotes Grid/Table */}
      {quotes.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-white/40 mb-4">
            <Search size={48} className="mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Geen quotes gevonden</h3>
            <p className="text-sm">Maak je eerste quote aan om te beginnen</p>
          </div>
          <Button
            onClick={() => {/* TODO: Navigate to create quote */}}
            className="bg-primary-500 hover:bg-primary-600"
          >
            <Plus size={16} className="mr-2" />
            Eerste Quote Aanmaken
          </Button>
        </div>
      ) : (
        <div className={cn(
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
            : 'space-y-2'
        )}>
          {quotes.map((quote) => (
            <QuoteCard
              key={quote.id}
              quote={quote}
              onEdit={handleEditQuote}
              onDelete={handleDeleteQuote}
              onPreview={onPreviewQuote || (() => {})}
            />
          ))}
        </div>
      )}
    </div>
  );
}; 