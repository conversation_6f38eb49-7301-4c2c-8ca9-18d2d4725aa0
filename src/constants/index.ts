// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
export const API_TIMEOUT = 30000; // 30 seconds
export const API_RETRY_ATTEMPTS = 3;

// Application Configuration
export const APP_NAME = 'AI.qoute+crm';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'AI-powered quote generation and customer management system';

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// File Upload
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg'
];

// Quote Configuration
export const QUOTE_VALIDITY_DAYS = 30;
export const DEFAULT_TAX_RATE = 21; // Belgian VAT rate
export const CURRENCY = 'EUR';

// AI Configuration
export const AI_RESPONSE_TIMEOUT = 30000; // 30 seconds
export const AI_MAX_RETRIES = 3;
export const AI_CONFIDENCE_THRESHOLD = 0.8;

// Voice Recognition
export const VOICE_RECOGNITION_TIMEOUT = 60000; // 60 seconds
export const SUPPORTED_LANGUAGES = ['nl', 'fr'] as const;

// UI Configuration
export const ANIMATION_DURATION = 300;
export const TOAST_DURATION = 5000;
export const DEBOUNCE_DELAY = 300;

// Breakpoints
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Status Colors
export const STATUS_COLORS = {
  draft: 'gray',
  sent: 'blue',
  accepted: 'green',
  rejected: 'red',
  expired: 'yellow',
} as const;

// Project Types
export const PROJECT_TYPES = {
  renovation: {
    label: 'Renovatie',
    icon: 'hammer',
    color: 'blue',
  },
  new_build: {
    label: 'Nieuwbouw',
    icon: 'building',
    color: 'green',
  },
  maintenance: {
    label: 'Onderhoud',
    icon: 'wrench',
    color: 'orange',
  },
  consultation: {
    label: 'Consultatie',
    icon: 'users',
    color: 'purple',
  },
} as const;

// Quote Categories
export const QUOTE_CATEGORIES = [
  'Elektriciteit',
  'Sanitair',
  'Verwarming',
  'Ventilatie',
  'Isolatie',
  'Dakwerken',
  'Metselwerk',
  'Schrijnwerk',
  'Schilderwerk',
  'Tegelwerk',
  'Grondwerken',
  'Andere',
] as const;

// Units
export const UNITS = [
  'stuk',
  'm²',
  'm³',
  'm',
  'kg',
  'l',
  'uur',
  'dag',
  'week',
  'maand',
] as const;

// Countries
export const COUNTRIES = {
  BE: {
    name: 'België',
    flag: '🇧🇪',
    currency: 'EUR',
    vatRate: 21,
  },
  FR: {
    name: 'Frankrijk',
    flag: '🇫🇷',
    currency: 'EUR',
    vatRate: 20,
  },
} as const;

// Localization
export const SUPPORTED_LOCALES = {
  nl: {
    name: 'Nederlands',
    flag: '🇳🇱',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    currencyFormat: '€ #,##0.00',
  },
  fr: {
    name: 'Français',
    flag: '🇫🇷',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    currencyFormat: '#,##0.00 €',
  },
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  info: {
    icon: 'info',
    color: 'blue',
  },
  success: {
    icon: 'check-circle',
    color: 'green',
  },
  warning: {
    icon: 'alert-triangle',
    color: 'yellow',
  },
  error: {
    icon: 'x-circle',
    color: 'red',
  },
} as const;

// AI Assistant (Rita) Configuration
export const RITA_CONFIG = {
  name: 'Rita',
  avatar: '/assets/images/rita-avatar.png',
  welcomeMessage: {
    nl: 'Hallo! Ik ben Rita, je AI assistent. Hoe kan ik je helpen met je offerte?',
    fr: 'Bonjour! Je suis Rita, votre assistante IA. Comment puis-je vous aider avec votre devis?',
  },
  capabilities: [
    {
      name: 'quote_generation',
      label: 'Offerte Generatie',
      description: 'Genereer professionele offertes op basis van spraak of tekst',
    },
    {
      name: 'price_optimization',
      label: 'Prijs Optimalisatie',
      description: 'Optimaliseer prijzen op basis van marktdata',
    },
    {
      name: 'suggestion',
      label: 'Suggesties',
      description: 'Krijg suggesties voor aanvullende werken',
    },
    {
      name: 'analysis',
      label: 'Analyse',
      description: 'Analyseer projecten en trends',
    },
  ],
} as const;

// Export Templates
export const EXPORT_TEMPLATES = {
  default: {
    name: 'Standaard Template',
    description: 'Basis offerte template',
    filename: 'offerte-template-default.html',
  },
  professional: {
    name: 'Professioneel Template',
    description: 'Uitgebreide template met logo en styling',
    filename: 'offerte-template-professional.html',
  },
  minimal: {
    name: 'Minimaal Template',
    description: 'Eenvoudige template voor snelle offertes',
    filename: 'offerte-template-minimal.html',
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  network: {
    nl: 'Netwerkfout. Controleer je internetverbinding.',
    fr: 'Erreur réseau. Vérifiez votre connexion internet.',
  },
  unauthorized: {
    nl: 'Je bent niet geautoriseerd om deze actie uit te voeren.',
    fr: 'Vous n\'êtes pas autorisé à effectuer cette action.',
  },
  notFound: {
    nl: 'De gevraagde resource is niet gevonden.',
    fr: 'La ressource demandée n\'a pas été trouvée.',
  },
  serverError: {
    nl: 'Er is een serverfout opgetreden. Probeer het later opnieuw.',
    fr: 'Une erreur serveur s\'est produite. Réessayez plus tard.',
  },
  validation: {
    nl: 'Controleer de ingevoerde gegevens.',
    fr: 'Vérifiez les données saisies.',
  },
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  quoteCreated: {
    nl: 'Offerte succesvol aangemaakt!',
    fr: 'Devis créé avec succès!',
  },
  quoteSent: {
    nl: 'Offerte succesvol verzonden!',
    fr: 'Devis envoyé avec succès!',
  },
  customerAdded: {
    nl: 'Klant succesvol toegevoegd!',
    fr: 'Client ajouté avec succès!',
  },
  projectCreated: {
    nl: 'Project succesvol aangemaakt!',
    fr: 'Projet créé avec succès!',
  },
  settingsSaved: {
    nl: 'Instellingen opgeslagen!',
    fr: 'Paramètres enregistrés!',
  },
} as const; 