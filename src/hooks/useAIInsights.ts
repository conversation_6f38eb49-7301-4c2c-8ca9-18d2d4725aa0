import { useState, useEffect } from 'react';

export interface AIInsight {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  timestamp: string;
  actionText: string;
  actionUrl: string;
  icon: string;
  borderColor: string;
  isRead: boolean;
}

export interface AIInsightsStats {
  newLeads: number;
  potentialRevenue: number;
  totalInsights: number;
  unreadInsights: number;
}

export const useAIInsights = () => {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [stats, setStats] = useState<AIInsightsStats>({
    newLeads: 0,
    potentialRevenue: 0,
    totalInsights: 0,
    unreadInsights: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data - later replace with API calls
  const mockInsights: AIInsight[] = [
    {
      id: '1',
      title: 'Nieuwe <PERSON> Kans',
      description: 'AI detecteert groeiende vraag in renovatie sector. 3 potentiële leads geïdentificeerd.',
      priority: 'high',
      category: 'market',
      timestamp: '6 uur geleden',
      actionText: 'Bekijk leads',
      actionUrl: '/leads',
      icon: 'Eye',
      borderColor: 'border-red-500/30',
      isRead: false,
    },
    {
      id: '2',
      title: 'Automatisering Mogelijkheid',
      description: 'Repentitieve taken kunnen geautomatiseerd worden. Bespaar 8 uur/week.',
      priority: 'medium',
      category: 'automation',
      timestamp: '8 uur geleden',
      actionText: 'Setup automatisering',
      actionUrl: '/automation',
      icon: 'Target',
      borderColor: 'border-yellow-500/30',
      isRead: false,
    },
    {
      id: '3',
      title: 'Prijsoptimalisatie',
      description: 'AI stelt voor om prijzen met 5% te verhogen voor renovatie projecten.',
      priority: 'high',
      category: 'pricing',
      timestamp: '2 uur geleden',
      actionText: 'Bekijk analyse',
      actionUrl: '/pricing',
      icon: 'TrendingUp',
      borderColor: 'border-green-500/30',
      isRead: true,
    },
    {
      id: '4',
      title: 'Klant Retentie',
      description: '3 klanten tonen tekenen van ontevredenheid. Actie vereist.',
      priority: 'medium',
      category: 'retention',
      timestamp: '4 uur geleden',
      actionText: 'Bekijk klanten',
      actionUrl: '/customers',
      icon: 'Eye',
      borderColor: 'border-orange-500/30',
      isRead: false,
    },
    {
      id: '5',
      title: 'Efficiëntie Verbetering',
      description: 'Quote proces kan 30% sneller met AI assistentie.',
      priority: 'low',
      category: 'efficiency',
      timestamp: '1 dag geleden',
      actionText: 'Meer info',
      actionUrl: '/efficiency',
      icon: 'Zap',
      borderColor: 'border-blue-500/30',
      isRead: true,
    },
  ];

  // Load insights
  const loadInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setInsights(mockInsights);
      
      // Calculate stats
      const unreadCount = mockInsights.filter(insight => !insight.isRead).length;
      setStats({
        newLeads: 12,
        potentialRevenue: 8500,
        totalInsights: mockInsights.length,
        unreadInsights: unreadCount,
      });
    } catch (err) {
      setError('Fout bij het laden van AI Insights');
      console.error('Error loading AI insights:', err);
    } finally {
      setLoading(false);
    }
  };

  // Mark insight as read
  const markAsRead = (insightId: string) => {
    setInsights(prev => 
      prev.map(insight => 
        insight.id === insightId 
          ? { ...insight, isRead: true }
          : insight
      )
    );
    
    // Update stats
    setStats(prev => ({
      ...prev,
      unreadInsights: Math.max(0, prev.unreadInsights - 1),
    }));
  };

  // Mark all insights as read
  const markAllAsRead = () => {
    setInsights(prev => 
      prev.map(insight => ({ ...insight, isRead: true }))
    );
    
    setStats(prev => ({
      ...prev,
      unreadInsights: 0,
    }));
  };

  // Filter insights by priority
  const getInsightsByPriority = (priority: 'all' | 'high' | 'medium' | 'low') => {
    if (priority === 'all') {return insights;}
    return insights.filter(insight => insight.priority === priority);
  };

  // Get insights by category
  const getInsightsByCategory = (category: string) => {
    return insights.filter(insight => insight.category === category);
  };

  // Load insights on mount
  useEffect(() => {
    loadInsights();
  }, []);

  return {
    insights,
    stats,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    getInsightsByPriority,
    getInsightsByCategory,
    refreshInsights: loadInsights,
  };
}; 