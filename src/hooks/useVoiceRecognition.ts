import { useState, useEffect, useCallback } from 'react';

interface VoiceRecognitionState {
  isListening: boolean;
  transcript: string;
  isSupported: boolean;
  error: string | null;
}

interface UseVoiceRecognitionReturn extends VoiceRecognitionState {
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  setTranscript: (text: string) => void;
}

export const useVoiceRecognition = (): UseVoiceRecognitionReturn => {
  const [state, setState] = useState<VoiceRecognitionState>({
    isListening: false,
    transcript: '',
    isSupported: false,
    error: null,
  });

  const [recognition, setRecognition] = useState<any>(null);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = true;
      recognitionInstance.interimResults = true;
      recognitionInstance.lang = 'nl-NL';

      recognitionInstance.onstart = () => {
        setState(prev => ({
          ...prev,
          isListening: true,
          error: null,
        }));
      };

      recognitionInstance.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setState(prev => ({
          ...prev,
          transcript: prev.transcript + finalTranscript + interimTranscript,
        }));
      };

      recognitionInstance.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        setState(prev => ({
          ...prev,
          isListening: false,
          error: `Spraakherkenning fout: ${event.error}`,
        }));
      };

      recognitionInstance.onend = () => {
        setState(prev => ({
          ...prev,
          isListening: false,
        }));
      };

      setRecognition(recognitionInstance);
      setState(prev => ({ ...prev, isSupported: true }));
    } else {
      setState(prev => ({
        ...prev,
        isSupported: false,
        error: 'Spraakherkenning wordt niet ondersteund in deze browser',
      }));
    }
  }, []);

  const startListening = useCallback(() => {
    if (recognition && state.isSupported) {
      try {
        recognition.start();
      } catch (error) {
        setState(prev => ({
          ...prev,
          error: 'Kon spraakherkenning niet starten',
        }));
      }
    }
  }, [recognition, state.isSupported]);

  const stopListening = useCallback(() => {
    if (recognition) {
      recognition.stop();
    }
  }, [recognition]);

  const resetTranscript = useCallback(() => {
    setState(prev => ({
      ...prev,
      transcript: '',
      error: null,
    }));
  }, []);

  const setTranscript = useCallback((text: string) => {
    setState(prev => ({
      ...prev,
      transcript: text,
    }));
  }, []);

  return {
    ...state,
    startListening,
    stopListening,
    resetTranscript,
    setTranscript,
  };
}; 