import { useState, useCallback } from 'react';

interface ImageAnalysisResult {
  description: string;
  materials: string[];
  dimensions?: string;
  quantity?: number;
  suggestedItems: QuoteItem[];
}

interface UseImageAnalysisReturn {
  isAnalyzing: boolean;
  results: ImageAnalysisResult | null;
  error: string | null;
  uploadImage: (file: File) => Promise<void>;
  resetResults: () => void;
}

// Mock AI analysis function - in real implementation, this would call an AI API
const analyzeImageWithAI = async (file: File): Promise<ImageAnalysisResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock analysis based on file name or random selection
  const mockAnalyses = [
    {
      description: "Houten kozijnen met dubbele beglazing",
      materials: ["Hout", "Glas", "Aluminium"],
      dimensions: "120x150cm",
      quantity: 4,
      suggestedItems: [
        {
          id: "1",
          description: "<PERSON><PERSON><PERSON> kozijnen met dubbele beglazing",
          quantity: 4,
          unitPrice: 450,
          total: 1800
        },
        {
          id: "2", 
          description: "Installatie en montage",
          quantity: 1,
          unitPrice: 200,
          total: 200
        }
      ]
    },
    {
      description: "Stalen deur met veiligheidsslot",
      materials: ["Staal", "Kunststof", "Metaal"],
      dimensions: "90x210cm", 
      quantity: 1,
      suggestedItems: [
        {
          id: "3",
          description: "Stalen deur met veiligheidsslot",
          quantity: 1,
          unitPrice: 850,
          total: 850
        },
        {
          id: "4",
          description: "Montage en afwerking",
          quantity: 1,
          unitPrice: 150,
          total: 150
        }
      ]
    },
    {
      description: "Betonnen fundering en metselwerk",
      materials: ["Beton", "Baksteen", "Cement"],
      dimensions: "10x5m",
      quantity: 1,
      suggestedItems: [
        {
          id: "5",
          description: "Betonnen fundering",
          quantity: 1,
          unitPrice: 2500,
          total: 2500
        },
        {
          id: "6",
          description: "Metselwerk en afwerking",
          quantity: 1,
          unitPrice: 1800,
          total: 1800
        }
      ]
    }
  ];

  // Select random analysis based on file name
  const index = file.name.length % mockAnalyses.length;
  return mockAnalyses[index];
};

export const useImageAnalysis = (): UseImageAnalysisReturn => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<ImageAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const uploadImage = useCallback(async (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Alleen afbeeldingen zijn toegestaan');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Bestand is te groot. Maximum grootte is 10MB');
      return;
    }

    try {
      setIsAnalyzing(true);
      setError(null);
      setResults(null);

      const analysisResult = await analyzeImageWithAI(file);
      setResults(analysisResult);
    } catch (err) {
      setError('Fout bij het analyseren van de afbeelding');
      console.error('Image analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const resetResults = useCallback(() => {
    setResults(null);
    setError(null);
  }, []);

  return {
    isAnalyzing,
    results,
    error,
    uploadImage,
    resetResults,
  };
}; 