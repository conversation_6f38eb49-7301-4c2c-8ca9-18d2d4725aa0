import { useEffect } from 'react';
import { useAppStore } from '@/stores/useAppStore';

export const useKeyboardShortcuts = () => {
  const { sidebarCollapsed, setSidebarCollapsed } = useAppStore();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + B to toggle sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(!sidebarCollapsed);
      }
      
      // Escape to close mobile sidebar
      if (event.key === 'Escape') {
        // This will be handled by the Sidebar component
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [sidebarCollapsed, setSidebarCollapsed]);
}; 