import { useCallback } from 'react';
import { useAppStore, Quote, QuoteItem } from '@/stores/useAppStore';
import { useToast } from '@/contexts/ToastContext';

export const useQuotes = () => {
  const {
    quotes,
    addQuote,
    updateQuote,
    deleteQuote,
    getQuoteById,
    getQuotesByStatus,
    getQuotesByCustomer,
    setLoading,
    setError,
  } = useAppStore();
  
  const { showToast } = useToast();

  // Create new quote
  const createQuote = useCallback(async (quoteData: Omit<Quote, 'id' | 'createdAt'>) => {
    try {
      setLoading(true);
      
      const newQuote: Quote = {
        ...quoteData,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date(),
      };
      
      addQuote(newQuote);
      
      showToast({
        type: 'success',
        title: 'Offerte aangemaakt',
        message: `Offerte ${newQuote.number} is succesvol aangemaakt.`,
      });
      
      return newQuote;
    } catch (error) {
      setError('Fout bij het aanmaken van offerte');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het aanmaken van de offerte.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [addQuote, setLoading, setError, showToast]);

  // Update quote
  const updateQuoteById = useCallback(async (id: string, updates: Partial<Quote>) => {
    try {
      setLoading(true);
      
      updateQuote(id, updates);
      
      showToast({
        type: 'success',
        title: 'Offerte bijgewerkt',
        message: 'Offerte is succesvol bijgewerkt.',
      });
    } catch (error) {
      setError('Fout bij het bijwerken van offerte');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het bijwerken van de offerte.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [updateQuote, setLoading, setError, showToast]);

  // Delete quote
  const deleteQuoteById = useCallback(async (id: string) => {
    try {
      setLoading(true);
      
      const quote = getQuoteById(id);
      deleteQuote(id);
      
      showToast({
        type: 'success',
        title: 'Offerte verwijderd',
        message: `Offerte ${quote?.number} is succesvol verwijderd.`,
      });
    } catch (error) {
      setError('Fout bij het verwijderen van offerte');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het verwijderen van de offerte.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [deleteQuote, getQuoteById, setLoading, setError, showToast]);

  // Add item to quote
  const addItemToQuote = useCallback((quoteId: string, item: Omit<QuoteItem, 'id'>) => {
    const quote = getQuoteById(quoteId);
    if (!quote) {return;}

    const newItem: QuoteItem = {
      ...item,
      id: Math.random().toString(36).substr(2, 9),
      total: item.quantity * item.unitPrice,
    };

    const updatedItems = [...quote.items, newItem];
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.total, 0);

    updateQuote(quoteId, {
      items: updatedItems,
      amount: totalAmount,
    });
  }, [getQuoteById, updateQuote]);

  // Remove item from quote
  const removeItemFromQuote = useCallback((quoteId: string, itemId: string) => {
    const quote = getQuoteById(quoteId);
    if (!quote) {return;}

    const updatedItems = quote.items.filter(item => item.id !== itemId);
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.total, 0);

    updateQuote(quoteId, {
      items: updatedItems,
      amount: totalAmount,
    });
  }, [getQuoteById, updateQuote]);

  return {
    quotes,
    createQuote,
    updateQuoteById,
    deleteQuoteById,
    getQuoteById,
    getQuotesByStatus,
    getQuotesByCustomer,
    addItemToQuote,
    removeItemFromQuote,
  };
}; 