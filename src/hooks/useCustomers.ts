import { useCallback } from 'react';
import { useAppStore, Customer } from '@/stores/useAppStore';
import { useToast } from '@/contexts/ToastContext';

export const useCustomers = () => {
  const {
    customers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomerById,
    setLoading,
    setError,
  } = useAppStore();
  
  const { showToast } = useToast();

  // Create new customer
  const createCustomer = useCallback(async (customerData: Omit<Customer, 'id' | 'createdAt'>) => {
    try {
      setLoading(true);
      
      const newCustomer: Customer = {
        ...customerData,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date(),
        status: 'active',
      };
      
      addCustomer(newCustomer);
      
      showToast({
        type: 'success',
        title: 'Klant toegevoegd',
        message: `Klant ${newCustomer.name} is succesvol toegevoegd.`,
      });
      
      return newCustomer;
    } catch (error) {
      setError('Fout bij het toevoegen van klant');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het toevoegen van de klant.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [addCustomer, setLoading, setError, showToast]);

  // Update customer
  const updateCustomerById = useCallback(async (id: string, updates: Partial<Customer>) => {
    try {
      setLoading(true);
      
      updateCustomer(id, updates);
      
      showToast({
        type: 'success',
        title: 'Klant bijgewerkt',
        message: 'Klantgegevens zijn succesvol bijgewerkt.',
      });
    } catch (error) {
      setError('Fout bij het bijwerken van klant');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het bijwerken van de klantgegevens.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [updateCustomer, setLoading, setError, showToast]);

  // Delete customer
  const deleteCustomerById = useCallback(async (id: string) => {
    try {
      setLoading(true);
      
      const customer = getCustomerById(id);
      deleteCustomer(id);
      
      showToast({
        type: 'success',
        title: 'Klant verwijderd',
        message: `Klant ${customer?.name} is succesvol verwijderd.`,
      });
    } catch (error) {
      setError('Fout bij het verwijderen van klant');
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het verwijderen van de klant.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [deleteCustomer, getCustomerById, setLoading, setError, showToast]);

  // Search customers
  const searchCustomers = useCallback((query: string) => {
    if (!query.trim()) {return customers;}
    
    const searchTerm = query.toLowerCase();
    return customers.filter(customer => 
      customer.name.toLowerCase().includes(searchTerm) ||
      customer.email.toLowerCase().includes(searchTerm) ||
      customer.company.toLowerCase().includes(searchTerm)
    );
  }, [customers]);

  // Get active customers
  const getActiveCustomers = useCallback(() => {
    return customers.filter(customer => customer.status === 'active');
  }, [customers]);

  return {
    customers,
    createCustomer,
    updateCustomerById,
    deleteCustomerById,
    getCustomerById,
    searchCustomers,
    getActiveCustomers,
  };
}; 