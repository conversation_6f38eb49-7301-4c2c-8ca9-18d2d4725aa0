export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  vatRate: number;
  vatAmount: number;
}

export interface Invoice {
  id: string;
  number: string;
  customerId: string;
  customerName: string;
  customerEmail?: string;
  customerAddress?: string;
  projectName?: string;
  invoiceDate: string;
  dueDate: string;
  vatPercentage: number;
  subtotal: number;
  vatAmount: number;
  total: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  items: InvoiceItem[];
  pdfUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateInvoiceRequest {
  quoteId?: string;
  customerId: string;
  projectName?: string;
  invoiceDate?: string;
  dueDate?: string;
  vatPercentage?: number;
  manualItems?: {
    description: string;
    quantity: number;
    unitPrice: number;
  }[];
}

export interface CreateInvoiceResponse {
  success: boolean;
  data?: {
    invoiceId: string;
    pdfUrl: string;
    invoice: Invoice;
  };
  message?: string;
}

export interface AIInvoiceRequest {
  quoteId?: string;
  manualItems?: {
    description: string;
    quantity: number;
    unitPrice: number;
  }[];
  customerId: string;
  projectName?: string;
  invoiceDate?: string;
  dueDate?: string;
  vatPercentage?: number;
}

export interface AIInvoiceResponse {
  success: boolean;
  data?: {
    invoiceId: string;
    pdfUrl: string;
    invoice: Invoice;
    aiDescription?: string;
  };
  message?: string;
}

export interface InvoiceFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  type?: 'sales' | 'purchase' | 'all';
  status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'all';
  contactId?: string;
  projectId?: string;
  onlyPaid?: boolean;
  onlyOverdue?: boolean;
}

export interface ExportConfig {
  format: 'excel' | 'csv' | 'exact' | 'twinfield';
  dateRange: {
    start: string;
    end: string;
  };
  includeTypes: ('sales' | 'purchase')[];
  onlyPaid: boolean;
  groupByMonth: boolean;
  categories?: string[];
  projects?: string[];
}

export interface InvoiceAnalytics {
  totalOutstanding: number;
  monthlyTotal: number;
  overdueAmount: number;
  averagePaymentTime: number;
  cashFlowProjection: {
    incoming: number;
    outgoing: number;
    net: number;
  };
  profitAnalysis: {
    grossProfit: number;
    profitMargin: number;
    expensesByCategory: Record<string, number>;
  };
}

export interface InvoiceTemplate {
  id: string;
  name: string;
  type: 'sales' | 'purchase';
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    vatRate: number;
  }>;
  defaultNotes?: string;
  accountingCategory: string;
}

export interface OCRScanResult {
  success: boolean;
  extractedData?: {
    supplierName?: string;
    invoiceNumber?: string;
    issueDate?: Date;
    dueDate?: Date;
    totalAmount?: number;
    vatAmount?: number;
    items?: Array<{
      description: string;
      quantity: number;
      unitPrice: number;
      total: number;
    }>;
  };
  confidence: number;
  error?: string;
} 