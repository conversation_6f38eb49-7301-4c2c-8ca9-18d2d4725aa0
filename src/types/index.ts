// User types
export interface User {
  id: string;
  email: string;
  name: string;
  company: string;
  role: 'admin' | 'user' | 'manager';
  avatar?: string;
  preferences: UserPreferences;
  subscription: Subscription;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  language: 'nl' | 'fr';
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationSettings;
  currency: 'EUR';
  timezone: string;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  quoteUpdates: boolean;
  customerUpdates: boolean;
  systemUpdates: boolean;
}

export interface Subscription {
  plan: 'free' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled';
  startDate: Date;
  endDate: Date;
  features: string[];
}

// Quote types
export interface Quote {
  id: string;
  number: string;
  title: string;
  description: string;
  customer: Customer;
  project?: Project;
  items: QuoteItem[];
  subtotal: number;
  tax: number;
  total: number;
  currency: 'EUR';
  status: QuoteStatus;
  validityDays: number;
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  notes?: string;
  attachments: Attachment[];
  aiGenerated: boolean;
  aiSuggestions: AISuggestion[];
}

export interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
  category: string;
  aiSuggested: boolean;
}

export type QuoteStatus = 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';

export interface AISuggestion {
  id: string;
  type: 'item' | 'price' | 'description';
  content: string;
  confidence: number;
  applied: boolean;
}

// Customer types
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address: Address;
  company?: string;
  vatNumber?: string;
  notes?: string;
  tags: string[];
  status: 'active' | 'inactive' | 'prospect';
  createdAt: Date;
  updatedAt: Date;
  quotes: Quote[];
  projects: Project[];
}

export interface Address {
  street: string;
  number: string;
  postalCode: string;
  city: string;
  country: 'BE' | 'FR';
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// Project types
export interface Project {
  id: string;
  name: string;
  description: string;
  customer: Customer;
  address: Address;
  type: ProjectType;
  status: ProjectStatus;
  startDate?: Date;
  endDate?: Date;
  budget?: number;
  photos: Attachment[];
  quotes: Quote[];
  notes: ProjectNote[];
  createdAt: Date;
  updatedAt: Date;
}

export type ProjectType = 'renovation' | 'new_build' | 'maintenance' | 'consultation';
export type ProjectStatus = 'planning' | 'active' | 'completed' | 'cancelled';

export interface ProjectNote {
  id: string;
  content: string;
  author: User;
  createdAt: Date;
  updatedAt: Date;
}

// AI Assistant types
export interface AIAssistant {
  id: string;
  name: 'Rita';
  status: 'online' | 'offline' | 'busy';
  capabilities: AICapability[];
  currentTask?: AITask;
  performance: AIPerformance;
}

export interface AICapability {
  name: string;
  description: string;
  available: boolean;
  accuracy: number;
}

export interface AITask {
  id: string;
  type: 'quote_generation' | 'price_optimization' | 'suggestion' | 'analysis';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  input: any;
  output?: any;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface AIPerformance {
  responseTime: number;
  accuracy: number;
  successRate: number;
  totalRequests: number;
}

// File types
export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'pdf' | 'document' | 'audio';
  url: string;
  size: number;
  uploadedAt: Date;
  uploadedBy: User;
}

// Dashboard types
export interface DashboardStats {
  totalQuotes: number;
  totalCustomers: number;
  totalRevenue: number;
  quoteConversionRate: number;
  averageQuoteValue: number;
  monthlyGrowth: {
    quotes: number;
    customers: number;
    revenue: number;
  };
}

export interface Activity {
  id: string;
  type: 'quote_created' | 'quote_sent' | 'quote_accepted' | 'customer_added' | 'project_created';
  title: string;
  description: string;
  user: User;
  relatedEntity?: {
    type: 'quote' | 'customer' | 'project';
    id: string;
    name: string;
  };
  createdAt: Date;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  read: boolean;
  actionUrl?: string;
  createdAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'date' | 'file';
  required: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// Voice recognition types
export interface VoiceInput {
  text: string;
  confidence: number;
  language: 'nl' | 'fr';
  timestamp: Date;
}

// Export types
export interface ExportOptions {
  format: 'pdf' | 'csv' | 'json';
  includeAttachments: boolean;
  language: 'nl' | 'fr';
  template?: string;
} 