import toast from 'react-hot-toast'

/**
 * Enhanced fetch wrapper met automatische error handling en toast notifications
 * Voor gebruik in de hele applicatie voor consistente error handling
 */
export interface FetchOptions extends RequestInit {
  showToast?: boolean
  toastMessage?: string
  errorMessage?: string
}

export interface FetchResponse<T = any> {
  data: T
  status: number
  ok: boolean
  headers: Headers
}

/**
 * Enhanced fetch functie met automatische error handling
 * @param url - API endpoint URL
 * @param options - Fetch opties inclusief toast configuratie
 * @returns Promise met response data en metadata
 */
export async function fetchWithToast<T = any>(
  url: string,
  options: FetchOptions = {}
): Promise<FetchResponse<T>> {
  const {
    showToast = true,
    toastMessage,
    errorMessage,
    ...fetchOptions
  } = options

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
    })

    // Log alle responses voor debugging
    console.log(`[${response.status}] ${response.statusText} - ${url}`)

    if (!response.ok) {
      const errorData = await response.text().catch(() => 'Unknown error')
      
      // Console error voor developers
      console.error(`❌ Fetch Error [${response.status}]:`, {
        url,
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        timestamp: new Date().toISOString(),
      })

      // Toast notification voor gebruikers
      if (showToast) {
        const message = errorMessage || toastMessage || `Error ${response.status}: ${response.statusText}`
        toast.error(message, {
          duration: 5000,
          position: 'top-right',
        })
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // Parse response data
    let data: T
    const contentType = response.headers.get('content-type')
    
    if (contentType?.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text() as T
    }

    // Success toast (optioneel)
    if (showToast && toastMessage) {
      toast.success(toastMessage, {
        duration: 3000,
        position: 'top-right',
      })
    }

    return {
      data,
      status: response.status,
      ok: response.ok,
      headers: response.headers,
    }
  } catch (error) {
    // Network errors en andere exceptions
    console.error('❌ Network Error:', {
      url,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    })

    if (showToast) {
      const message = errorMessage || 'Netwerk fout - probeer het opnieuw'
      toast.error(message, {
        duration: 5000,
        position: 'top-right',
      })
    }

    throw error
  }
}

/**
 * GET request helper
 */
export async function get<T = any>(
  url: string,
  options: Omit<FetchOptions, 'method'> = {}
): Promise<FetchResponse<T>> {
  return fetchWithToast<T>(url, { ...options, method: 'GET' })
}

/**
 * POST request helper
 */
export async function post<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method' | 'body'> = {}
): Promise<FetchResponse<T>> {
  return fetchWithToast<T>(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * PUT request helper
 */
export async function put<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method' | 'body'> = {}
): Promise<FetchResponse<T>> {
  return fetchWithToast<T>(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * DELETE request helper
 */
export async function del<T = any>(
  url: string,
  options: Omit<FetchOptions, 'method'> = {}
): Promise<FetchResponse<T>> {
  return fetchWithToast<T>(url, { ...options, method: 'DELETE' })
}

/**
 * PATCH request helper
 */
export async function patch<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method' | 'body'> = {}
): Promise<FetchResponse<T>> {
  return fetchWithToast<T>(url, {
    ...options,
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
  })
}

// Export default voor backward compatibility
export default fetchWithToast 