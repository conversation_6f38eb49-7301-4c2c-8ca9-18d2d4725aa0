import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { QuoteList } from '@/components/quotes';
import { EnhancedQuoteForm } from '@/components/quotes/EnhancedQuoteForm';
import { Quote } from '@/stores/useAppStore';
import { useToast } from '@/contexts/ToastContext';
import { Eye, FileText, Download, Share2, Edit, Trash2, Plus, Search, Filter, Sparkles } from 'lucide-react';

interface Invoice {
  id: string;
  number: string;
  customer: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  dueDate: string;
  createdAt: string;
}

export const Quotes: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [showQuoteForm, setShowQuoteForm] = useState(false);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [activeTab, setActiveTab] = useState<'quotes' | 'invoices'>('quotes');
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<Quote | null>(null);

  const handleCreateQuote = () => {
    setSelectedQuote(null);
    setFormMode('create');
    setShowQuoteForm(true);
  };

  const handleEditQuote = (quote: Quote) => {
    setSelectedQuote(quote);
    setFormMode('edit');
    setShowQuoteForm(true);
  };

  const handleCloseForm = () => {
    setShowQuoteForm(false);
    setSelectedQuote(null);
  };

  const handleQuoteSaved = (quote: Quote) => {
    showToast({
      type: 'success',
      title: formMode === 'create' ? 'Offerte Aangemaakt' : 'Offerte Bijgewerkt',
      message: `Offerte ${quote.number} is succesvol ${formMode === 'create' ? 'aangemaakt' : 'bijgewerkt'}.`,
    });
    handleCloseForm();
  };

  const handleQuoteError = (error: string) => {
    showToast({
      type: 'error',
      title: 'Fout',
      message: error,
    });
  };

  // Mock data voor facturen
  const mockInvoices: Invoice[] = [
    {
      id: '1',
      number: 'INV-2024-001',
      customer: 'Jan Vermeulen',
      amount: 2500,
      status: 'paid',
      dueDate: '2024-01-15',
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      number: 'INV-2024-002',
      customer: 'Marie Dubois',
      amount: 1800,
      status: 'sent',
      dueDate: '2024-01-20',
      createdAt: '2024-01-05'
    },
    {
      id: '3',
      number: 'INV-2024-003',
      customer: 'Piet Janssen',
      amount: 3200,
      status: 'overdue',
      dueDate: '2024-01-10',
      createdAt: '2024-01-03'
    }
  ];

  const handlePreviewQuote = (quote: Quote) => {
    setPreviewData(quote);
    setShowPreview(true);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewData(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-400 bg-green-400/10';
      case 'sent': return 'text-blue-400 bg-blue-400/10';
      case 'overdue': return 'text-red-400 bg-red-400/10';
      case 'draft': return 'text-gray-400 bg-gray-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  return (
    <div className="flex gap-6 h-full">
      {/* Main Content */}
      <div className="flex-1 space-y-6">
        {/* Header with AI Quote Wizard button */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white">Quotes & Facturen</h1>
            <p className="text-gray-400">Beheer al je offertes en facturen</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/quotes/new-ai')}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 rounded-lg transition-colors text-white font-medium"
            >
              <Sparkles className="w-4 h-4" />
              AI Quote Wizard
            </button>
            <button
              onClick={() => setShowQuoteForm(true)}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors text-white font-medium"
            >
              <Plus className="w-4 h-4" />
              Nieuwe Quote
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-2">
          <div className="flex space-x-2">
            <button
              onClick={() => setActiveTab('quotes')}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'quotes'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              <FileText className="h-4 w-4 mr-2" />
              Offertes
            </button>
            <button
              onClick={() => setActiveTab('invoices')}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'invoices'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              <FileText className="h-4 w-4 mr-2" />
              Facturen
            </button>
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'quotes' ? (
          <div className="space-y-6">
            {/* Show QuoteList for displaying quotes */}
            <QuoteList />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Invoices Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-white">Facturen</h1>
                <p className="text-gray-300">Beheer uw facturen en betalingen</p>
              </div>
              <div className="flex items-center space-x-3">
                <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <Plus className="h-4 w-4 mr-2" />
                  Nieuwe Factuur
                </button>
              </div>
            </div>

            {/* Invoices List */}
            <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Zoeken in facturen..."
                        className="pl-10 pr-4 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <button className="flex items-center px-3 py-2 text-gray-300 hover:text-white transition-colors">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  {mockInvoices.map((invoice) => (
                    <div
                      key={invoice.id}
                      className="flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:bg-slate-800/50 transition-colors cursor-pointer"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                          <FileText className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-white font-medium">{invoice.number}</h3>
                          <p className="text-gray-400 text-sm">{invoice.customer}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-white font-medium">€{invoice.amount.toLocaleString()}</p>
                          <p className="text-gray-400 text-sm">Vervaldatum: {invoice.dueDate}</p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                          {invoice.status === 'paid' ? 'Betaald' : 
                           invoice.status === 'sent' ? 'Verzonden' : 
                           invoice.status === 'overdue' ? 'Achterstallig' : 'Concept'}
                        </span>
                                                 <div className="flex items-center space-x-2">
                           <button 
                             className="p-2 text-gray-400 hover:text-white transition-colors"
                             title="Bekijk factuur"
                             aria-label="Bekijk factuur"
                           >
                             <Eye className="h-4 w-4" />
                           </button>
                           <button 
                             className="p-2 text-gray-400 hover:text-white transition-colors"
                             title="Download factuur"
                             aria-label="Download factuur"
                           >
                             <Download className="h-4 w-4" />
                           </button>
                           <button 
                             className="p-2 text-gray-400 hover:text-white transition-colors"
                             title="Bewerk factuur"
                             aria-label="Bewerk factuur"
                           >
                             <Edit className="h-4 w-4" />
                           </button>
                         </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Preview Panel */}
      {showPreview && previewData && (
        <div className="w-96 bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Preview</h2>
            <button
              onClick={handleClosePreview}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>

          <div className="space-y-4">
                         <div className="bg-slate-800/30 rounded-lg p-4">
               <h3 className="text-white font-medium mb-2">Offerte Details</h3>
               <div className="space-y-2 text-sm">
                 <div className="flex justify-between">
                   <span className="text-gray-400">Nummer:</span>
                   <span className="text-white">{previewData.number}</span>
                 </div>
                 <div className="flex justify-between">
                   <span className="text-gray-400">Klant:</span>
                   <span className="text-white">{previewData.customerId}</span>
                 </div>
                 <div className="flex justify-between">
                   <span className="text-gray-400">Status:</span>
                   <span className="text-white">{previewData.status}</span>
                 </div>
               </div>
             </div>

             <div className="bg-slate-800/30 rounded-lg p-4">
               <h3 className="text-white font-medium mb-2">Offerte Items</h3>
               <div className="space-y-2 text-sm">
                 {previewData.items?.map((item, index) => (
                   <div key={index} className="flex justify-between">
                     <span className="text-gray-400">{item.description}</span>
                     <span className="text-white">€{item.unitPrice}</span>
                   </div>
                 ))}
               </div>
             </div>

             <div className="bg-slate-800/30 rounded-lg p-4">
               <h3 className="text-white font-medium mb-2">Totaal</h3>
               <div className="text-right">
                 <p className="text-2xl font-bold text-white">€{previewData.items?.reduce((sum, item) => sum + item.unitPrice, 0) || 0}</p>
               </div>
             </div>

            <div className="flex space-x-3">
              <button className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </button>
              <button className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Share2 className="h-4 w-4 mr-2" />
                Verstuur
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* EnhancedQuoteForm Modal */}
      {showQuoteForm && (
        <EnhancedQuoteForm
          quote={selectedQuote || undefined}
          mode={formMode}
          onSaved={handleQuoteSaved}
          onError={handleQuoteError}
          onCancel={handleCloseForm}
          isOpen={showQuoteForm}
          onClose={handleCloseForm}
        />
      )}
    </div>
  );
}; 