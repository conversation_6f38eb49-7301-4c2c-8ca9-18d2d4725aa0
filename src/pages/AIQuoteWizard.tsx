import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Upload, 
  Plus, 
  Trash2, 
  Calculator, 
  FileText, 
  Download, 
  Send,
  Eye,
  Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/contexts/ToastContext';
import { quoteApi } from '@/api/quotes';
import { Quote, QuoteItem } from '@/stores/useAppStore';

interface Dimension {
  id: string;
  length: number;
  width: number;
  height: number;
  quantity: number;
  description: string;
}

interface PhotoFile {
  id: string;
  file: File;
  preview: string;
}

interface AIQuoteResponse {
  title: string;
  items: QuoteItem[];
  subtotal: number;
  vat: number;
  total: number;
  description: string;
}

export const AIQuoteWizard: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [title, setTitle] = useState('');
  const [dimensions, setDimensions] = useState<Dimension[]>([
    { id: '1', length: 0, width: 0, height: 0, quantity: 1, description: '' }
  ]);
  const [photos, setPhotos] = useState<PhotoFile[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedQuote, setGeneratedQuote] = useState<AIQuoteResponse | null>(null);

  // Add dimension field
  const addDimension = () => {
    const newId = (dimensions.length + 1).toString();
    setDimensions([...dimensions, {
      id: newId,
      length: 0,
      width: 0,
      height: 0,
      quantity: 1,
      description: ''
    }]);
  };

  // Remove dimension field
  const removeDimension = (id: string) => {
    if (dimensions.length > 1) {
      setDimensions(dimensions.filter(d => d.id !== id));
    }
  };

  // Update dimension
  const updateDimension = (id: string, field: keyof Dimension, value: string | number) => {
    setDimensions(dimensions.map(d => 
      d.id === id ? { ...d, [field]: value } : d
    ));
  };

  // Handle photo upload
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const photo: PhotoFile = {
            id: Date.now().toString(),
            file,
            preview: e.target?.result as string
          };
          setPhotos([...photos, photo]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  // Remove photo
  const removePhoto = (id: string) => {
    setPhotos(photos.filter(p => p.id !== id));
  };

  // Generate quote with AI
  const generateQuote = async () => {
    if (!title.trim()) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Vul een titel in voor het project.'
      });
      return;
    }

    if (dimensions.every(d => d.length === 0 && d.width === 0 && d.height === 0)) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Vul minimaal één afmeting in.'
      });
      return;
    }

    if (photos.length === 0) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Upload minimaal één foto.'
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Convert photos to base64
      const photoUrls = await Promise.all(
        photos.map(async (photo) => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string);
            reader.readAsDataURL(photo.file);
          });
        })
      );

      // Call AI Quote API
      const response = await fetch('/api/ai-quote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          dimensions,
          photos: photoUrls
        })
      });

      if (!response.ok) {
        throw new Error('AI Quote generatie mislukt');
      }

      const result: AIQuoteResponse = await response.json();
      setGeneratedQuote(result);

      showToast({
        type: 'success',
        title: 'Succes!',
        message: 'AI Quote succesvol gegenereerd.'
      });

    } catch (error) {
      console.error('Error generating quote:', error);
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het genereren van de offerte.'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Save quote to database
  const saveQuote = async () => {
    if (!generatedQuote) {return;}

    try {
      const quote = await quoteApi.createQuote({
        customerId: 'ai-generated',
        customerName: 'AI Generated',
        projectName: generatedQuote.title,
        items: generatedQuote.items,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      });

      showToast({
        type: 'success',
        title: 'Opgeslagen!',
        message: 'Offerte succesvol opgeslagen in de database.'
      });

      // Navigate to quotes page
      navigate('/quotes');
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Fout',
        message: 'Er is een fout opgetreden bij het opslaan.'
      });
    }
  };

  return (
    <div className="flex gap-6 h-full">
      {/* Form Section */}
      <div className="flex-1 space-y-6">
        <div className="flex items-center gap-3 mb-6">
          <Sparkles className="w-8 h-8 text-blue-500" />
          <div>
            <h1 className="text-3xl font-bold text-white">AI Quote Wizard</h1>
            <p className="text-gray-400">Genereer automatisch een offerte met AI</p>
          </div>
        </div>

        <Card className="bg-glass-dark backdrop-blur-sm border border-white/20 p-6">
          {/* Project Title */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-white mb-2">
              Project Titel *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Bijv. Badkamer Renovatie, Keuken Uitbreiding..."
              className="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Dimensions */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <label className="text-sm font-medium text-white">
                Project Afmetingen *
              </label>
              <Button
                onClick={addDimension}
                variant="outline"
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Afmeting Toevoegen
              </Button>
            </div>

            <div className="space-y-4">
              {dimensions.map((dimension, index) => (
                <div key={dimension.id} className="bg-slate-800/30 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium">Afmeting {index + 1}</h4>
                    {dimensions.length > 1 && (
                      <Button
                        onClick={() => removeDimension(dimension.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Lengte (m)</label>
                      <input
                        type="number"
                        step="0.1"
                        value={dimension.length}
                        onChange={(e) => updateDimension(dimension.id, 'length', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Breedte (m)</label>
                      <input
                        type="number"
                        step="0.1"
                        value={dimension.width}
                        onChange={(e) => updateDimension(dimension.id, 'width', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Hoogte (m)</label>
                      <input
                        type="number"
                        step="0.1"
                        value={dimension.height}
                        onChange={(e) => updateDimension(dimension.id, 'height', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Aantal</label>
                      <input
                        type="number"
                        min="1"
                        value={dimension.quantity}
                        onChange={(e) => updateDimension(dimension.id, 'quantity', parseInt(e.target.value) || 1)}
                        className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="mt-3">
                    <label className="block text-xs text-gray-400 mb-1">Beschrijving (optioneel)</label>
                    <input
                      type="text"
                      value={dimension.description}
                      onChange={(e) => updateDimension(dimension.id, 'description', e.target.value)}
                      placeholder="Bijv. Badkamer tegels, Keuken kasten..."
                      className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Photo Upload */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-white mb-2">
              Project Foto's *
            </label>
            
            <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                id="photo-upload"
              />
              <label htmlFor="photo-upload" className="cursor-pointer">
                <Upload className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-white font-medium mb-2">Upload project foto's</p>
                <p className="text-gray-400 text-sm">Sleep foto's hierheen of klik om te selecteren</p>
              </label>
            </div>

            {/* Photo Preview */}
            {photos.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                {photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <img
                      src={photo.preview}
                      alt="Project photo"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      onClick={() => removePhoto(photo.id)}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Generate Button */}
          <Button
            onClick={generateQuote}
            disabled={isGenerating}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3"
          >
            {isGenerating ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                AI Quote Genereren...
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" />
                AI Quote Genereren
              </>
            )}
          </Button>
        </Card>
      </div>

      {/* Preview Section */}
      {generatedQuote && (
        <div className="w-96">
          <Card className="bg-glass-dark backdrop-blur-sm border border-white/20 p-6 sticky top-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">AI Quote Preview</h2>
              <FileText className="w-6 h-6 text-blue-500" />
            </div>

            {/* Quote Details */}
            <div className="space-y-4 mb-6">
              <div>
                <h3 className="text-white font-semibold mb-2">{generatedQuote.title}</h3>
                <p className="text-gray-400 text-sm">
                  Vervaldatum: {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('nl-NL')}
                </p>
              </div>

              {/* Items Table */}
              <div className="bg-slate-800/30 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">Items</h4>
                <div className="space-y-2">
                  {generatedQuote.items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-gray-300">
                        {item.quantity}x {item.description}
                      </span>
                      <span className="text-white font-medium">
                        €{item.total.toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Totals */}
              <div className="border-t border-slate-700 pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Subtotaal:</span>
                  <span className="text-white">€{generatedQuote.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">BTW (21%):</span>
                  <span className="text-white">€{generatedQuote.vat.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t border-slate-700 pt-2">
                  <span className="text-white">Totaal:</span>
                  <span className="text-blue-400">€{generatedQuote.total.toFixed(2)}</span>
                </div>
              </div>

              {/* Description */}
              <div className="bg-slate-800/30 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Beschrijving</h4>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {generatedQuote.description}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={saveQuote}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <FileText className="w-4 h-4 mr-2" />
                Opslaan als Quote
              </Button>
              
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" size="sm" className="bg-slate-700 border-slate-600 text-white">
                  <Eye className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" className="bg-slate-700 border-slate-600 text-white">
                  <Download className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" className="bg-slate-700 border-slate-600 text-white">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}; 