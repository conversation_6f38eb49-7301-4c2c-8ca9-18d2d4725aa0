import React, { useState } from 'react';
import { CustomerList, CustomerForm } from '@/components/customers';
import { Customer } from '@/stores/useAppStore';

export const Customers: React.FC = () => {
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  const handleCreateCustomer = () => {
    setSelectedCustomer(null);
    setFormMode('create');
    setShowCustomerForm(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setFormMode('edit');
    setShowCustomerForm(true);
  };

  const handleCloseForm = () => {
    setShowCustomerForm(false);
    setSelectedCustomer(null);
  };

  return (
    <div className="space-y-6">
      <CustomerList />
      
      <CustomerForm
        isOpen={showCustomerForm}
        onClose={handleCloseForm}
        customer={selectedCustomer}
        mode={formMode}
      />
    </div>
  );
}; 