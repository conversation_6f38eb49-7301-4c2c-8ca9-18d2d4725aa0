import React, { useState } from 'react';
import { 
  Settings as SettingsIcon, 
  User, 
  Building2, 
  Bell, 
  Shield,
  Database,
  Download,
  Upload
} from 'lucide-react';
import { UserSettings, CompanySettings } from '@/components/settings';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/contexts/ToastContext';

// Settings tabs
const settingsTabs = [
  {
    id: 'user',
    label: 'Gebruiker',
    icon: User,
    component: UserSettings,
  },
  {
    id: 'company',
    label: 'Bedrijf',
    icon: Building2,
    component: CompanySettings,
  },
  {
    id: 'notifications',
    label: 'Notificaties',
    icon: Bell,
    component: () => <div className="text-gray-300">Notificatie instellingen komen binnenkort</div>,
  },
  {
    id: 'security',
    label: 'Beveiliging',
    icon: Shield,
    component: () => <div className="text-gray-300">Beveiligingsinstellingen komen binnenkort</div>,
  },
  {
    id: 'data',
    label: 'Gegevens',
    icon: Database,
    component: () => <div className="text-gray-300">Gegevensbeheer komt binnenkort</div>,
  },
];

export const Settings: React.FC = () => {
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState('user');

  // Get current tab component
  const currentTab = settingsTabs.find(tab => tab.id === activeTab);
  const CurrentComponent = currentTab?.component;

  // Handle data export
  const handleExport = () => {
    showToast({
      type: 'info',
      title: 'Export',
      message: 'Gegevens worden geëxporteerd...',
    });
  };

  // Handle data import
  const handleImport = () => {
    showToast({
      type: 'info',
      title: 'Import',
      message: 'Gegevens worden geïmporteerd...',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white flex items-center">
            <SettingsIcon className="h-8 w-8 mr-3" />
            Instellingen
          </h1>
          <p className="text-gray-300">Beheer uw account en applicatie instellingen</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="glass" onClick={handleImport}>
            <Upload className="h-4 w-4 mr-2" />
            Importeren
          </Button>
          <Button variant="glass" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exporteren
          </Button>
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-2">
        <div className="flex flex-wrap gap-2">
          {settingsTabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Settings Content */}
      <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-6">
        {CurrentComponent && <CurrentComponent />}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-4">
          <div className="flex items-center mb-3">
            <Database className="h-5 w-5 mr-2 text-blue-400" />
            <h3 className="text-white font-semibold">Gegevens Backup</h3>
          </div>
          <p className="text-gray-300 text-sm mb-3">
            Maak een backup van al uw gegevens voor veiligheid
          </p>
          <Button variant="glass" size="sm" onClick={handleExport}>
            Backup Maken
          </Button>
        </div>

        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-4">
          <div className="flex items-center mb-3">
            <Shield className="h-5 w-5 mr-2 text-green-400" />
            <h3 className="text-white font-semibold">Beveiliging</h3>
          </div>
          <p className="text-gray-300 text-sm mb-3">
            Beheer uw wachtwoord en beveiligingsinstellingen
          </p>
          <Button 
            variant="glass" 
            size="sm"
            onClick={() => setActiveTab('security')}
          >
            Beveiliging
          </Button>
        </div>

        <div className="bg-glass-light backdrop-blur-md border border-white/20 rounded-xl p-4">
          <div className="flex items-center mb-3">
            <Bell className="h-5 w-5 mr-2 text-yellow-400" />
            <h3 className="text-white font-semibold">Notificaties</h3>
          </div>
          <p className="text-gray-300 text-sm mb-3">
            Configureer uw notificatie voorkeuren
          </p>
          <Button 
            variant="glass" 
            size="sm"
            onClick={() => setActiveTab('notifications')}
          >
            Notificaties
          </Button>
        </div>
      </div>
    </div>
  );
}; 