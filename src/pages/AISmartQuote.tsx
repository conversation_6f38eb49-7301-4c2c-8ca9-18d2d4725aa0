import { Button } from '@/components/ui/Button';
import { FormInput } from '@/components/ui/FormInput';
import { cn } from '@/utils/cn';
import {
  Bot,
  Camera,
  Check, ChevronDown,
  Edit3,
  FileText,
  Mail,
  MapPin,
  MessageCircle,
  Send,
  User,
  X
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
}

interface ProjectPhoto {
  file: File;
  preview: string;
  id: string;
}

interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export const AISmartQuote: React.FC = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [projectTitle, setProjectTitle] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    email: '',
    phone: '',
    address: '',
    company: ''
  });
  const [photos, setPhotos] = useState<ProjectPhoto[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedQuote, setGeneratedQuote] = useState<any>(null);
  const [quoteItems, setQuoteItems] = useState<QuoteItem[]>([]);
  const [projectDescription, setProjectDescription] = useState('');
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editingDescription, setEditingDescription] = useState(false);
  const [sendMethod, setSendMethod] = useState<'email' | 'whatsapp'>('email');

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const newPhoto: ProjectPhoto = {
          file,
          preview: event.target?.result as string,
          id: Date.now().toString() + Math.random()
        };
        setPhotos(prev => [...prev, newPhoto]);
      };
      reader.readAsDataURL(file);
    });
  };

  // Add validation function
  const validateForm = () => {
    const errors: string[] = [];
    
    if (!projectTitle.trim()) {errors.push('Project titel is verplicht');}
    if (!customerInfo.name.trim()) {errors.push('Klant naam is verplicht');}
    if (!customerInfo.email.trim()) {errors.push('Email is verplicht');}
    if (photos.length === 0) {errors.push('Minimaal 1 foto is verplicht');}
    
    return errors;
  };

  const generateAIQuote = async () => {
    const errors = validateForm();
    
    if (errors.length > 0) {
      alert('Vul alle verplichte velden in:\n' + errors.join('\n'));
      return;
    }

    setIsGenerating(true);

    try {
      // Simulate AI generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockItems: QuoteItem[] = [
        {
          id: '1',
          description: 'Materialen en benodigdheden',
          quantity: 1,
          unitPrice: 2500,
          total: 2500
        },
        {
          id: '2',
          description: 'Arbeidskosten (inclusief installatie)',
          quantity: 1,
          unitPrice: 1800,
          total: 1800
        },
        {
          id: '3',
          description: 'Project management en afwerking',
          quantity: 1,
          unitPrice: 700,
          total: 700
        }
      ];

      const subtotal = mockItems.reduce((sum, item) => sum + item.total, 0);
      const vat = subtotal * 0.21;
      const total = subtotal + vat;

      setQuoteItems(mockItems);
      setProjectDescription(`Professionele ${projectTitle.toLowerCase()} uitgevoerd volgens hoogste standaarden. Inclusief alle materialen, vakkundige installatie en garantie.`);
      
      setGeneratedQuote({
        title: projectTitle,
        items: mockItems,
        subtotal,
        vat,
        total,
        description: `Professionele ${projectTitle.toLowerCase()} uitgevoerd volgens hoogste standaarden.`
      });

    } catch (error) {
      alert('Er is een fout opgetreden bij het genereren van de offerte.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleItemEdit = (itemId: string, field: keyof QuoteItem, value: any) => {
    setQuoteItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const updated = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          updated.total = updated.quantity * updated.unitPrice;
        }
        return updated;
      }
      return item;
    }));
  };

  const recalculateTotal = () => {
    const subtotal = quoteItems.reduce((sum, item) => sum + item.total, 0);
    const vat = subtotal * 0.21;
    const total = subtotal + vat;
    
    setGeneratedQuote(prev => ({
      ...prev,
      subtotal,
      vat,
      total,
      items: quoteItems
    }));
  };

  useEffect(() => {
    if (quoteItems.length > 0) {
      recalculateTotal();
    }
  }, [quoteItems]);

  const sendQuote = async () => {
    if (!generatedQuote) {return;}

    try {
      // Simulate sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert(`Offerte verzonden via ${sendMethod === 'whatsapp' ? 'WhatsApp' : 'email'}!`);
    } catch (error) {
      alert('Er is een fout opgetreden bij het verzenden.');
    }
  };

  const handleClose = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      window.location.href = '/';
    }
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <div 
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-4 shadow-2xl cursor-pointer hover:scale-105 transition-transform"
          onClick={() => setIsMinimized(false)}
        >
          <Bot className="w-6 h-6 text-white" />
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 flex items-center justify-center p-4">
      <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">AI Smart Quote</h2>
              <p className="text-gray-400 text-sm">Intelligente offerte generator</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(true)}
              className="text-gray-400 hover:text-white p-2"
              title="Minimaliseren"
            >
              <ChevronDown className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-400 hover:text-white p-2"
              title="Sluiten"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row h-[calc(90vh-80px)]">
          {/* Input Panel */}
          <div className="w-full lg:w-1/2 p-6 border-b lg:border-b-0 lg:border-r border-white/10 overflow-y-auto space-y-6">
            {/* Project Info */}
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Project Informatie
              </h3>
              <FormInput
                label="Project Titel"
                value={projectTitle}
                onChange={(e) => setProjectTitle(e.target.value)}
                placeholder="Bijv. Badkamer renovatie, Keuken verbouwing..."
              />
            </div>

            {/* Customer Info */}
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                Klant Gegevens
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  label="Naam"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Voor- en achternaam"
                />
                <FormInput
                  label="Bedrijf (optioneel)"
                  value={customerInfo.company || ''}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, company: e.target.value }))}
                  placeholder="Bedrijfsnaam"
                />
                <FormInput
                  label="Email"
                  type="email"
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
                <FormInput
                  label="Telefoon"
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+31 6 12345678"
                />
              </div>
              <div className="mt-4">
                <FormInput
                  label="Adres"
                  value={customerInfo.address}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Straat, huisnummer, postcode, plaats"
                  icon={MapPin}
                />
              </div>
            </div>

            {/* Photo Upload */}
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Camera className="w-5 h-5 mr-2" />
                Project Foto's
              </h3>
              <div className="space-y-4">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                  id="photo-upload"
                />
                <label
                  htmlFor="photo-upload"
                  className="flex items-center justify-center w-full h-32 border-2 border-dashed border-white/30 rounded-lg cursor-pointer hover:border-white/50 transition-colors"
                >
                  <div className="text-center">
                    <Camera className="w-8 h-8 text-white/60 mx-auto mb-2" />
                    <p className="text-white/60">Klik om foto's te uploaden</p>
                  </div>
                </label>
                
                {photos.length > 0 && (
                  <div className="grid grid-cols-2 gap-4">
                    {photos.map((photo) => (
                      <div key={photo.id} className="relative">
                        <img
                          src={photo.preview}
                          alt="Project foto"
                          className="w-full h-24 object-cover rounded-lg"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <Button
              onClick={generateAIQuote}
              disabled={isGenerating}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Bot className="w-5 h-5 mr-2 animate-spin" />
                  AI Genereert Offerte...
                </>
              ) : (
                <>
                  <Bot className="w-5 h-5 mr-2" />
                  Genereer AI Offerte
                </>
              )}
            </Button>
          </div>

          {/* Quote Preview Panel */}
          <div className="w-full lg:w-1/2 p-6 overflow-y-auto">
            {generatedQuote ? (
              <div className="space-y-6">
                {/* Editable Description */}
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-white">Project Beschrijving</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingDescription(!editingDescription)}
                      className="text-gray-400 hover:text-white p-1"
                    >
                      {editingDescription ? <Check className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
                    </Button>
                  </div>
                  
                  {editingDescription ? (
                    <textarea
                      value={projectDescription}
                      onChange={(e) => setProjectDescription(e.target.value)}
                      className="w-full p-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white resize-none"
                      rows={3}
                      placeholder="Beschrijf het project..."
                    />
                  ) : (
                    <p className="text-gray-300 text-sm leading-relaxed">
                      {projectDescription || generatedQuote.description}
                    </p>
                  )}
                </div>

                {/* Editable Quote Items */}
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h3 className="font-semibold text-white mb-4">Offerte Items</h3>
                  
                  <div className="space-y-3">
                    {quoteItems.map((item) => (
                      <div key={item.id} className="group bg-slate-800/30 rounded-lg p-3 border border-slate-700/50 hover:border-slate-600 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 space-y-2">
                            {/* Editable Description */}
                            {editingItem === item.id ? (
                              <input
                                type="text"
                                value={item.description}
                                onChange={(e) => handleItemEdit(item.id, 'description', e.target.value)}
                                className="w-full bg-transparent border-b border-blue-400 text-white text-sm focus:outline-none"
                                onBlur={() => setEditingItem(null)}
                                onKeyDown={(e) => e.key === 'Enter' && setEditingItem(null)}
                                autoFocus
                              />
                            ) : (
                              <p 
                                className="text-white text-sm cursor-pointer hover:text-blue-300 transition-colors"
                                onClick={() => setEditingItem(item.id)}
                              >
                                {item.description}
                              </p>
                            )}
                            
                            {/* Editable Quantity & Price */}
                            <div className="flex items-center space-x-4 text-xs">
                              <div className="flex items-center space-x-1">
                                <span className="text-gray-400">Aantal:</span>
                                <input
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => handleItemEdit(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                                  className="w-16 bg-transparent border-b border-gray-600 text-white text-center focus:border-blue-400 focus:outline-none"
                                  step="0.1"
                                />
                              </div>
                              
                              <div className="flex items-center space-x-1">
                                <span className="text-gray-400">Prijs:</span>
                                <span className="text-gray-400">€</span>
                                <input
                                  type="number"
                                  value={item.unitPrice}
                                  onChange={(e) => handleItemEdit(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                                  className="w-20 bg-transparent border-b border-gray-600 text-white text-center focus:border-blue-400 focus:outline-none"
                                  step="0.01"
                                />
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <p className="text-white font-semibold">€{item.total.toFixed(2)}</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                              className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-white p-1"
                            >
                              <Edit3 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Totals */}
                  <div className="mt-6 pt-4 border-t border-white/10 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">Subtotaal:</span>
                      <span className="text-white">€{generatedQuote.subtotal?.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">BTW (21%):</span>
                      <span className="text-white">€{generatedQuote.vat?.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold pt-2 border-t border-white/10">
                      <span className="text-white">Totaal:</span>
                      <span className="text-green-400">€{generatedQuote.total?.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Send Options */}
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h3 className="font-semibold text-white mb-4">Verzenden</h3>
                  
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <button
                      onClick={() => setSendMethod('email')}
                      className={cn(
                        'p-3 rounded-lg border transition-all duration-200',
                        sendMethod === 'email'
                          ? 'bg-blue-600/20 border-blue-500/50 text-blue-300'
                          : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10'
                      )}
                    >
                      <Mail className="w-4 h-4 mx-auto mb-1" />
                      <span className="text-xs">Email</span>
                    </button>
                    
                    <button
                      onClick={() => setSendMethod('whatsapp')}
                      className={cn(
                        'p-3 rounded-lg border transition-all duration-200',
                        sendMethod === 'whatsapp'
                          ? 'bg-green-600/20 border-green-500/50 text-green-300'
                          : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10'
                      )}
                    >
                      <MessageCircle className="w-4 h-4 mx-auto mb-1" />
                      <span className="text-xs">WhatsApp</span>
                    </button>
                  </div>

                  <Button
                    onClick={sendQuote}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 transition-all duration-200"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Verstuur Offerte
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4">
                  <Bot className="w-16 h-16 text-white/20 mx-auto" />
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">AI Quote Generator</h3>
                    <p className="text-gray-400 text-sm">
                      Vul de gegevens in om een slimme offerte te genereren
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AISmartQuote;
