import React, { useState, useEffect, useRef } from 'react';
import { 
  Mail, 
  Send, 
  Paperclip, 
  Trash2, 
  Search, 
  Filter, 
  Plus,
  Edit,
  Eye,
  Download,
  Share2,
  Settings,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  Building,
  Calendar,
  Tag,
  Star,
  Archive,
  Inbox,
  Send as SendIcon,
  FileText,
  Trash,
  Reply,
  Forward,
  ChevronDown,
  ChevronUp,
  MoreHorizontal,
  X,
  Command
} from 'lucide-react';

interface Email {
  id: string;
  from: string;
  to: string[];
  subject: string;
  body: string;
  timestamp: Date;
  status: 'draft' | 'sent' | 'failed' | 'scheduled';
  priority: 'low' | 'medium' | 'high';
  attachments?: string[];
  provider: 'outlook' | 'gmail' | 'cloudflare';
  tags: string[];
  threadId?: string;
  isRead: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  category: 'quote' | 'follow-up' | 'invoice' | 'general';
}

const mockEmails: Email[] = [
  {
    id: '1',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Offerte voor renovatie project',
    body: '<PERSON><PERSON> Jan, Hierbij ontvangt u onze offerte voor het renovatie project...',
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    status: 'sent',
    priority: 'high',
    provider: 'outlook',
    tags: ['quote', 'renovatie'],
    threadId: 'thread-1',
    isRead: false
  },
  {
    id: '2',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Factuur #2024-001',
    body: 'Geachte mevrouw Dubois, Hierbij ontvangt u onze factuur...',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    status: 'draft',
    priority: 'medium',
    provider: 'gmail',
    tags: ['factuur'],
    threadId: 'thread-2',
    isRead: true
  },
  {
    id: '3',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Re: Offerte voor renovatie project',
    body: 'Bedankt voor de offerte. Ik heb een paar vragen...',
    timestamp: new Date(Date.now() - 1000 * 60 * 15),
    status: 'sent',
    priority: 'high',
    provider: 'outlook',
    tags: ['quote', 'renovatie'],
    threadId: 'thread-1',
    isRead: false
  }
];

const emailTemplates: EmailTemplate[] = [
  {
    id: '1',
    name: 'Offerte Template',
    subject: 'Offerte voor {project}',
    body: 'Beste {naam},\n\nHierbij ontvangt u onze offerte voor {project}.\n\nMet vriendelijke groet,\n{bedrijf}',
    category: 'quote'
  },
  {
    id: '2',
    name: 'Follow-up Template',
    subject: 'Vervolg op onze offerte',
    body: 'Beste {naam},\n\nHeeft u al de tijd gehad om onze offerte te bekijken?\n\nMet vriendelijke groet,\n{bedrijf}',
    category: 'follow-up'
  },
  {
    id: '3',
    name: 'Factuur Template',
    subject: 'Factuur #{factuurnummer}',
    body: 'Geachte {naam},\n\nHierbij ontvangt u onze factuur #{factuurnummer}.\n\nMet vriendelijke groet,\n{bedrijf}',
    category: 'invoice'
  }
];

const emailProviders = [
  { id: 'outlook', name: 'Outlook', icon: '📧', color: 'bg-blue-600' },
  { id: 'gmail', name: 'Gmail', icon: '📨', color: 'bg-red-600' },
  { id: 'cloudflare', name: 'Cloudflare', icon: '☁️', color: 'bg-orange-600' }
];

const folders = [
  { id: 'inbox', name: 'Inbox', icon: Inbox, count: 12 },
  { id: 'sent', name: 'Verzonden', icon: SendIcon, count: 8 },
  { id: 'drafts', name: 'Concepten', icon: FileText, count: 3 },
  { id: 'archive', name: 'Archief', icon: Archive, count: 45 },
  { id: 'trash', name: 'Prullenbak', icon: Trash, count: 0 }
];

export const Email: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState('outlook');
  const [emails, setEmails] = useState<Email[]>(mockEmails);
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [showGlobalSearch, setShowGlobalSearch] = useState(false);
  const [composeEmail, setComposeEmail] = useState({
    to: '',
    subject: '',
    body: '',
    priority: 'medium' as const,
    attachments: [] as string[]
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('inbox');
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set());
  const [replyText, setReplyText] = useState('');
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const emailListRef = useRef<HTMLDivElement>(null);

  // Hotkeys
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Global search (Ctrl+K)
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setShowGlobalSearch(true);
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }

      // Email navigation (j/k)
      if (e.key === 'j' || e.key === 'k') {
        e.preventDefault();
        const emailElements = emailListRef.current?.querySelectorAll('[data-email-id]');
        if (!emailElements) {return;}

        const currentIndex = Array.from(emailElements).findIndex(el => 
          el.getAttribute('data-email-id') === selectedEmail?.id
        );

        if (e.key === 'j') {
          const nextIndex = Math.min(currentIndex + 1, emailElements.length - 1);
          const nextEmail = emails.find(email => 
            email.id === emailElements[nextIndex]?.getAttribute('data-email-id')
          );
          if (nextEmail) {setSelectedEmail(nextEmail);}
        } else if (e.key === 'k') {
          const prevIndex = Math.max(currentIndex - 1, 0);
          const prevEmail = emails.find(email => 
            email.id === emailElements[prevIndex]?.getAttribute('data-email-id')
          );
          if (prevEmail) {setSelectedEmail(prevEmail);}
        }
      }

      // Reply (r)
      if (e.key === 'r' && selectedEmail && !isComposing) {
        e.preventDefault();
        setIsReplying(true);
        setReplyText('');
      }

      // Search (/)
      if (e.key === '/' && !isComposing && !isReplying) {
        e.preventDefault();
        setSearchTerm('');
        setTimeout(() => {
          const searchInput = document.querySelector('input[placeholder*="zoeken"]') as HTMLInputElement;
          searchInput?.focus();
        }, 100);
      }

      // Escape to close modals
      if (e.key === 'Escape') {
        if (showGlobalSearch) {setShowGlobalSearch(false);}
        if (isComposing) {setIsComposing(false);}
        if (isReplying) {setIsReplying(false);}
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedEmail, isComposing, isReplying, showGlobalSearch, emails]);

  const handleSendEmail = () => {
    const newEmail: Email = {
      id: Date.now().toString(),
      from: '<EMAIL>',
      to: composeEmail.to.split(',').map(email => email.trim()),
      subject: composeEmail.subject,
      body: composeEmail.body,
      timestamp: new Date(),
      status: 'sent',
      priority: composeEmail.priority,
      provider: selectedProvider as any,
      tags: [],
      isRead: false
    };

    setEmails([newEmail, ...emails]);
    setIsComposing(false);
    setComposeEmail({ to: '', subject: '', body: '', priority: 'medium', attachments: [] });
  };

  const handleReply = () => {
    if (!selectedEmail) {return;}
    
    const replyEmail: Email = {
      id: Date.now().toString(),
      from: '<EMAIL>',
      to: [selectedEmail.from],
      subject: `Re: ${selectedEmail.subject}`,
      body: replyText,
      timestamp: new Date(),
      status: 'sent',
      priority: 'medium',
      provider: selectedProvider as any,
      tags: [],
      threadId: selectedEmail.threadId,
      isRead: false
    };

    setEmails([replyEmail, ...emails]);
    setIsReplying(false);
    setReplyText('');
  };

  const handleTemplateSelect = (template: EmailTemplate) => {
    setComposeEmail({
      ...composeEmail,
      subject: template.subject,
      body: template.body
    });
    setIsComposing(true);
  };

  const toggleThreadExpansion = (threadId: string) => {
    const newExpanded = new Set(expandedThreads);
    if (newExpanded.has(threadId)) {
      newExpanded.delete(threadId);
    } else {
      newExpanded.add(threadId);
    }
    setExpandedThreads(newExpanded);
  };

  const getPriorityBadge = (priority: string) => {
    const styles = {
      high: 'bg-red-500/20 text-red-400 border-red-500/30',
      medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      low: 'bg-green-500/20 text-green-400 border-green-500/30'
    };
    return styles[priority as keyof typeof styles] || styles.medium;
  };

  const filteredEmails = emails.filter(email => 
    email.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    email.to.some(to => to.toLowerCase().includes(searchTerm.toLowerCase())) ||
    email.from.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group emails by thread
  const threadGroups = filteredEmails.reduce((acc, email) => {
    const threadId = email.threadId || email.id;
    if (!acc[threadId]) {
      acc[threadId] = [];
    }
    acc[threadId].push(email);
    return acc;
  }, {} as Record<string, Email[]>);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Global Search Modal */}
      {showGlobalSearch && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20">
          <div className="bg-slate-800 rounded-xl border border-slate-700 w-full max-w-2xl mx-4">
            <div className="p-4 border-b border-slate-700">
              <div className="flex items-center space-x-3">
                <Command className="w-5 h-5 text-slate-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Zoeken in alle emails..."
                  className="flex-1 bg-transparent text-white placeholder-slate-400 focus:outline-none"
                />
                <button
                  onClick={() => setShowGlobalSearch(false)}
                  className="text-slate-400 hover:text-white"
                  aria-label="Sluiten"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <p className="text-slate-400 text-sm">Gebruik Ctrl+K om snel te zoeken</p>
            </div>
          </div>
        </div>
      )}

      {/* Header with Account Tabs */}
      <div className="border-b border-slate-700 bg-slate-800/50 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-6">
            <h1 className="text-xl font-bold text-white">Email</h1>
            
            {/* Account Tabs */}
            <div className="flex space-x-1">
              {emailProviders.map((provider) => (
                <button
                  key={provider.id}
                  onClick={() => setSelectedProvider(provider.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedProvider === provider.id
                      ? `${provider.color} text-white`
                      : 'bg-slate-700 text-slate-300 hover:text-white'
                  }`}
                >
                  <span>{provider.icon}</span>
                  <span>{provider.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Global Search Button */}
            <button
              onClick={() => setShowGlobalSearch(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-slate-700 text-slate-300 rounded-lg hover:text-white transition-colors"
              aria-label="Global zoeken"
            >
              <Search className="w-4 h-4" />
              <span className="hidden sm:inline">Zoeken</span>
              <span className="text-xs bg-slate-600 px-2 py-1 rounded">⌘K</span>
            </button>

            {/* Compose Button */}
            <button
              onClick={() => setIsComposing(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
              aria-label="Nieuwe email"
            >
              <Plus className="w-4 h-4" />
              <span>Nieuwe Email</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content with Three Column Layout */}
      <div className="h-[calc(100vh-80px)] flex">
        {/* Left Panel - Folders */}
        <div className="w-64 bg-slate-800/50 backdrop-blur-sm border-r border-slate-700">
          <div className="p-4">
            <h3 className="text-sm font-semibold text-slate-300 mb-3">Mappen</h3>
            <div className="space-y-1">
              {folders.map((folder) => {
                const Icon = folder.icon;
                return (
                  <button
                    key={folder.id}
                    onClick={() => setSelectedFolder(folder.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                      selectedFolder === folder.id
                        ? 'bg-blue-600/20 text-blue-400'
                        : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                    }`}
                    aria-label={`Open ${folder.name}`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-4 h-4" />
                      <span>{folder.name}</span>
                    </div>
                    {folder.count > 0 && (
                      <span className="bg-slate-600 text-slate-300 text-xs px-2 py-1 rounded-full">
                        {folder.count}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Middle Panel - Email List */}
        <div className="w-96 bg-slate-800/50 backdrop-blur-sm border-r border-slate-700">
          <div className="p-4 border-b border-slate-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-slate-300">
                {folders.find(f => f.id === selectedFolder)?.name}
              </h3>
              <div className="flex items-center space-x-2">
                <button className="text-slate-400 hover:text-white" aria-label="Filter">
                  <Filter className="w-4 h-4" />
                </button>
                <button className="text-slate-400 hover:text-white" aria-label="Vernieuwen">
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Zoeken in emails..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              />
            </div>
          </div>

          {/* Email List */}
          <div ref={emailListRef} className="overflow-y-auto h-[calc(100%-80px)]">
            {Object.entries(threadGroups).map(([threadId, threadEmails]) => {
              const latestEmail = threadEmails[0];
              const isExpanded = expandedThreads.has(threadId);
              const hasMultipleEmails = threadEmails.length > 1;

              return (
                <div key={threadId} className="border-b border-slate-700/50">
                  <div
                    data-email-id={latestEmail.id}
                    onClick={() => setSelectedEmail(latestEmail)}
                    className={`p-4 cursor-pointer transition-colors ${
                      selectedEmail?.id === latestEmail.id
                        ? 'bg-blue-600/20 border-l-2 border-blue-400'
                        : 'hover:bg-slate-700/50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          {!latestEmail.isRead && (
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          )}
                          <h4 className={`text-sm font-medium truncate ${
                            latestEmail.isRead ? 'text-slate-300' : 'text-white'
                          }`}>
                            {latestEmail.subject}
                          </h4>
                        </div>
                        <p className="text-slate-400 text-xs truncate">
                          {latestEmail.from}
                        </p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={`text-xs px-2 py-1 rounded-full border ${getPriorityBadge(latestEmail.priority)}`}>
                            {latestEmail.priority}
                          </span>
                          {hasMultipleEmails && (
                            <span className="text-xs bg-slate-600 text-slate-300 px-2 py-1 rounded-full">
                              {threadEmails.length}
                            </span>
                          )}
                          <span className="text-slate-500 text-xs">
                            {latestEmail.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        {hasMultipleEmails && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleThreadExpansion(threadId);
                            }}
                            className="text-slate-400 hover:text-white"
                            aria-label={isExpanded ? "Thread inklappen" : "Thread uitklappen"}
                          >
                            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                          </button>
                        )}
                        {latestEmail.status === 'sent' && <CheckCircle className="w-4 h-4 text-green-400" />}
                        {latestEmail.status === 'draft' && <Clock className="w-4 h-4 text-yellow-400" />}
                        {latestEmail.status === 'failed' && <AlertCircle className="w-4 h-4 text-red-400" />}
                      </div>
                    </div>
                  </div>

                  {/* Thread Emails */}
                  {isExpanded && hasMultipleEmails && (
                    <div className="bg-slate-700/20">
                      {threadEmails.slice(1).map((email) => (
                        <div
                          key={email.id}
                          data-email-id={email.id}
                          onClick={() => setSelectedEmail(email)}
                          className={`p-3 ml-4 cursor-pointer transition-colors ${
                            selectedEmail?.id === email.id
                              ? 'bg-blue-600/20 border-l-2 border-blue-400'
                              : 'hover:bg-slate-700/50'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h5 className={`text-xs font-medium truncate ${
                                email.isRead ? 'text-slate-300' : 'text-white'
                              }`}>
                                {email.subject}
                              </h5>
                              <p className="text-slate-400 text-xs truncate">
                                {email.from}
                              </p>
                              <span className="text-slate-500 text-xs">
                                {email.timestamp.toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Right Panel - Email Content */}
        <div className="flex-1 bg-slate-800/50 backdrop-blur-sm">
          {isComposing ? (
            /* Compose Email */
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-slate-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">Nieuwe Email</h3>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setIsComposing(false)}
                      className="text-slate-400 hover:text-white transition-colors"
                      aria-label="Annuleren"
                    >
                      <X className="w-5 h-5" />
                    </button>
                    <button
                      onClick={handleSendEmail}
                      disabled={!composeEmail.to || !composeEmail.subject}
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
                      aria-label="Email versturen"
                    >
                      <Send className="w-4 h-4" />
                      <span>Versturen</span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {/* To */}
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">Aan</label>
                  <input
                    type="email"
                    value={composeEmail.to}
                    onChange={(e) => setComposeEmail({ ...composeEmail, to: e.target.value })}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">Onderwerp</label>
                  <input
                    type="text"
                    value={composeEmail.subject}
                    onChange={(e) => setComposeEmail({ ...composeEmail, subject: e.target.value })}
                    placeholder="Onderwerp van je email"
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-slate-300 text-sm font-medium mb-2">Prioriteit</label>
                  <select
                    value={composeEmail.priority}
                    onChange={(e) => setComposeEmail({ ...composeEmail, priority: e.target.value as any })}
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="Email prioriteit"
                  >
                    <option value="low">Laag</option>
                    <option value="medium">Medium</option>
                    <option value="high">Hoog</option>
                  </select>
                </div>

                {/* Body */}
                <div className="flex-1">
                  <label className="block text-slate-300 text-sm font-medium mb-2">Bericht</label>
                  <textarea
                    value={composeEmail.body}
                    onChange={(e) => setComposeEmail({ ...composeEmail, body: e.target.value })}
                    placeholder="Schrijf je bericht hier..."
                    className="w-full h-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    rows={12}
                  />
                </div>
              </div>
            </div>
          ) : selectedEmail ? (
            /* View Email */
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-slate-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">{selectedEmail.subject}</h3>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => setIsReplying(true)}
                      className="text-slate-400 hover:text-white transition-colors"
                      title="Beantwoorden (r)"
                      aria-label="Beantwoorden"
                    >
                      <Reply className="w-5 h-5" />
                    </button>
                    <button className="text-slate-400 hover:text-white transition-colors" aria-label="Doorsturen">
                      <Forward className="w-5 h-5" />
                    </button>
                    <button className="text-slate-400 hover:text-white transition-colors" aria-label="Meer opties">
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-4">
                  <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
                    <div>
                      <p className="text-white font-medium">Van: {selectedEmail.from}</p>
                      <p className="text-slate-400 text-sm">Aan: {selectedEmail.to.join(', ')}</p>
                      <p className="text-slate-400 text-sm">
                        {selectedEmail.timestamp.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full border ${getPriorityBadge(selectedEmail.priority)}`}>
                        {selectedEmail.priority}
                      </span>
                      <span className="text-xs bg-blue-600/20 text-blue-400 px-2 py-1 rounded-full">
                        {selectedEmail.provider}
                      </span>
                    </div>
                  </div>

                  <div className="p-4 bg-slate-700/50 rounded-lg">
                    <p className="text-white whitespace-pre-wrap">{selectedEmail.body}</p>
                  </div>

                  {selectedEmail.attachments && selectedEmail.attachments.length > 0 && (
                    <div>
                      <h4 className="text-slate-300 font-medium mb-2">Bijlagen</h4>
                      <div className="space-y-2">
                        {selectedEmail.attachments.map((attachment, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-slate-700/50 rounded">
                            <span className="text-slate-300 text-sm">{attachment}</span>
                            <button className="text-blue-400 hover:text-blue-300" aria-label="Download bijlage">
                              <Download className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Sticky Reply Editor */}
              {isReplying && (
                <div className="border-t border-slate-700 bg-slate-800/50">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-white">Beantwoorden</h4>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setIsReplying(false)}
                          className="text-slate-400 hover:text-white"
                          aria-label="Annuleren"
                        >
                          <X className="w-4 h-4" />
                        </button>
                        <button
                          onClick={handleReply}
                          disabled={!replyText.trim()}
                          className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                          aria-label="Antwoord versturen"
                        >
                          <Send className="w-3 h-3" />
                          <span>Versturen</span>
                        </button>
                      </div>
                    </div>
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Schrijf je antwoord..."
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                      rows={4}
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Empty State */
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Mail className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Selecteer een email</h3>
                <p className="text-slate-400">Kies een email uit de lijst om te bekijken</p>
                <div className="mt-4 text-xs text-slate-500 space-y-1">
                  <p>Hotkeys:</p>
                  <p>j/k - Navigeer emails</p>
                  <p>r - Beantwoorden</p>
                  <p>/ - Zoeken</p>
                  <p>⌘K - Global search</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 