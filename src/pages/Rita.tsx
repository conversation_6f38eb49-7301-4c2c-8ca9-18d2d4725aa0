import React, { useState } from 'react';
import { RitaInterface, RitaSuggestions } from '@/components/rita';

export const Rita: React.FC = () => {
  const [showSuggestions, setShowSuggestions] = useState(true);

  const handleSuggestionClick = (action: string) => {
    // TODO: Implement suggestion handling
    console.log('Suggestion clicked:', action);
  };

  return (
    <div className="h-full flex">
      {/* Main Chat Interface */}
      <div className={`flex-1 ${showSuggestions ? 'hidden lg:block' : 'block'}`}>
        <RitaInterface />
      </div>

      {/* Suggestions Sidebar */}
      {showSuggestions && (
        <div className="w-80 bg-glass-light backdrop-blur-md border-l border-white/20 p-6 overflow-y-auto">
          <RitaSuggestions onSuggestionClick={handleSuggestionClick} />
        </div>
      )}

      {/* Mobile Toggle Button */}
      <button
        className="lg:hidden fixed bottom-4 right-4 z-50 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center shadow-lg"
        onClick={() => setShowSuggestions(!showSuggestions)}
        title={showSuggestions ? 'Verberg suggesties' : 'Toon suggesties'}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {showSuggestions ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>
    </div>
  );
}; 