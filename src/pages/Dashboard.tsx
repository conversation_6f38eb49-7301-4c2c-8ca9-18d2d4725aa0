import React, { useState, useEffect } from 'react';
import { KPICard } from '../components/dashboard/KPICard';
import { Euro, Users, FileText, TrendingUp, Calendar, Clock } from 'lucide-react';

export const Dashboard = () => {
  const [kpiData, setKpiData] = useState({
    totalRevenue: { value: '€45.230', change: 12.5, trend: 'up' as const },
    activeQuotes: { value: '23', change: -5.2, trend: 'down' as const },
    customers: { value: '156', change: 8.1, trend: 'up' as const },
    conversionRate: { value: '68%', change: 2.3, trend: 'up' as const }
  });

  const [recentActivity] = useState([
    { id: 1, type: 'quote', title: 'Nieuwe offerte #2024-045', time: '5 min geleden', status: 'pending' },
    { id: 2, type: 'customer', title: 'Klant toegevoegd: Bouw<PERSON>rijf XYZ', time: '1 uur geleden', status: 'success' },
    { id: 3, type: 'payment', title: 'Betaling ontvangen €3.450', time: '2 uur geleden', status: 'success' },
    { id: 4, type: 'ai', title: 'Rita AI: 5 nieuwe suggesties', time: '3 uur geleden', status: 'info' }
  ]);

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="glass-card p-6">
        <h1 className="text-3xl font-bold text-white mb-2">
          Welkom terug! 👋
        </h1>
        <p className="text-slate-400">
          Hier is een overzicht van je bedrijf vandaag.
        </p>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Totale Omzet"
          value={kpiData.totalRevenue.value}
          change={kpiData.totalRevenue.change}
          trend={kpiData.totalRevenue.trend}
          icon={<Euro className="w-6 h-6 text-primary-400" />}
        />
        <KPICard
          title="Actieve Offertes"
          value={kpiData.activeQuotes.value}
          change={kpiData.activeQuotes.change}
          trend={kpiData.activeQuotes.trend}
          icon={<FileText className="w-6 h-6 text-blue-400" />}
        />
        <KPICard
          title="Klanten"
          value={kpiData.customers.value}
          change={kpiData.customers.change}
          trend={kpiData.customers.trend}
          icon={<Users className="w-6 h-6 text-green-400" />}
        />
        <KPICard
          title="Conversie Ratio"
          value={kpiData.conversionRate.value}
          change={kpiData.conversionRate.change}
          trend={kpiData.conversionRate.trend}
          icon={<TrendingUp className="w-6 h-6 text-purple-400" />}
        />
      </div>

      {/* Recent Activity */}
      <div className="glass-card p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Recente Activiteit</h2>
        <div className="space-y-3">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/5">
              <div className={cn(
                'w-2 h-2 rounded-full',
                activity.status === 'success' ? 'bg-green-400' :
                activity.status === 'pending' ? 'bg-yellow-400' :
                activity.status === 'info' ? 'bg-blue-400' : 'bg-slate-400'
              )} />
              <div className="flex-1">
                <p className="text-white text-sm">{activity.title}</p>
                <p className="text-slate-400 text-xs">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
