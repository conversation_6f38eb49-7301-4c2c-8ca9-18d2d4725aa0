// Mock API endpoint for AI Quote generation
// This would normally be a FastAPI endpoint

const mockMaterials = [
  'hout',
  'tegels', 
  'aluminium',
  'glas',
  'staal',
  'kunststof',
  'beton',
  'marmer'
];

const materialPrices = {
  'hout': 45,
  'tegels': 65,
  'aluminium': 85,
  'glas': 120,
  'staal': 95,
  'kunststof': 55,
  'beton': 35,
  'marmer': 150
};

const laborRates = {
  'hout': 25,
  'tegels': 35,
  'aluminium': 40,
  'glas': 50,
  'staal': 45,
  'kunststof': 30,
  'beton': 20,
  'marmer': 60
};

function detectMaterials(photos) {
  // Mock Vision AI analysis
  const detectedMaterials = [];
  for (let i = 0; i < Math.min(photos.length, 3); i++) {
    const randomMaterial = mockMaterials[Math.floor(Math.random() * mockMaterials.length)];
    if (!detectedMaterials.includes(randomMaterial)) {
      detectedMaterials.push(randomMaterial);
    }
  }
  return detectedMaterials.length > 0 ? detectedMaterials : ['hout', 'tegels'];
}

function calculateCosts(dimensions, materials) {
  const items = [];
  
  dimensions.forEach((dimension, index) => {
    const area = dimension.length * dimension.width;
    
    if (area > 0) {
      materials.forEach((material, materialIndex) => {
        const materialPrice = materialPrices[material] || 50;
        const laborRate = laborRates[material] || 30;
        const totalPrice = (materialPrice + laborRate) * area;
        
        items.push({
          id: `item-${index}-${materialIndex}`,
          description: `${material.charAt(0).toUpperCase() + material.slice(1)} werk - ${dimension.description || `${dimension.length}m x ${dimension.width}m`}`,
          quantity: dimension.quantity,
          unitPrice: materialPrice + laborRate,
          total: totalPrice * dimension.quantity
        });
      });
    }
  });
  
  return items;
}

function generateDescription(title, materials, items) {
  const totalAmount = items.reduce((sum, item) => sum + item.total, 0);
  const materialList = materials.join(', ');
  
  return `Professionale ${title.toLowerCase()} met hoogwaardige materialen (${materialList}). 
  Het project omvat alle benodigde materialen, professionele installatie en garantie. 
  De werkzaamheden worden uitgevoerd volgens de hoogste standaarden met aandacht voor detail en duurzaamheid. 
  Totaal projectwaarde: €${totalAmount.toFixed(2)}.`;
}

// Mock API endpoint
if (typeof window !== 'undefined') {
  // Browser environment - mock fetch
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options) {
    if (url === '/api/ai-quote' && options?.method === 'POST') {
      return new Promise((resolve) => {
        setTimeout(() => {
          const requestData = JSON.parse(options.body);
          
          // Step 1: Photo analysis
          const materials = detectMaterials(requestData.photos);
          
          // Step 2: Calculate costs
          const items = calculateCosts(requestData.dimensions, materials);
          
          // Step 3: Generate description
          const description = generateDescription(requestData.title, materials, items);
          
          // Step 4: Calculate totals
          const subtotal = items.reduce((sum, item) => sum + item.total, 0);
          const vat = subtotal * 0.21;
          const total = subtotal + vat;
          
          const response = {
            title: requestData.title,
            items,
            subtotal,
            vat,
            total,
            description
          };
          
          resolve({
            ok: true,
            json: () => Promise.resolve(response)
          });
        }, 2000); // Simulate API delay
      });
    }
    
    return originalFetch.apply(this, arguments);
  };
} 