# Quote.AI+CRM - Cursor IDE Rules

## Project Overview
- **Name**: Quote.AI+CRM
- **Type**: Intelligent Quote Generation & Customer Management System
- **Tech Stack**: React, TypeScript, Tailwind CSS, Vite
- **Target**: Construction professionals, contractors, service providers
- **Languages**: Dutch (primary), French (secondary)

## Core Functionality
- AI-powered quote generation using voice and text input
- Customer relationship management
- Property/project management
- Analytics and reporting
- Mobile-responsive dashboard
- Real-time notifications

## Development Guidelines

### Code Style
- Use TypeScript for all components
- Functional components with React hooks
- Tailwind CSS for styling (utility-first approach)
- ESLint + Prettier for code formatting
- Follow React best practices and patterns

### Component Structure
```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── dashboard/    # Dashboard-specific components
│   ├── quotes/       # Quote management components
│   ├── customers/    # Customer management components
│   └── layout/       # Layout components
├── hooks/            # Custom React hooks
├── utils/            # Utility functions
├── types/            # TypeScript type definitions
└── api/              # API integration layer
```

### UI/UX Standards
- **Colors**: Blue gradient theme (#3B82F6 to #1E40AF)
- **Dark Mode**: Primary interface (dark blue/navy backgrounds)
- **Mobile First**: Responsive design for all screen sizes
- **Accessibility**: WCAG 2.1 AA compliance
- **Icons**: Lucide React icon library
- **Typography**: Clean, professional fonts

### Key Features to Implement
1. **Dashboard**
   - KPI cards (Quotes, Customers, Revenue)
   - Quick action buttons
   - Recent activity feed
   - Charts and analytics

2. **Quote Management**
   - AI-powered quote generation
   - Voice input support
   - PDF export functionality
   - Template management

3. **Customer Management**
   - Contact information
   - Communication history
   - Project associations
   - Notes and tags

4. **Property Management**
   - Property details and photos
   - Location mapping
   - Associated quotes/projects
   - Maintenance history

5. **AI Assistant**
   - Natural language processing
   - Quote suggestions
   - Price optimization
   - Market insights

### Mobile Responsiveness
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Touch-friendly interface elements
- Swipe gestures for navigation
- Optimized for thumb navigation
- Progressive Web App (PWA) capabilities

### State Management
- React Context for global state
- Custom hooks for data fetching
- Local storage for user preferences
- Session management

### API Integration
- RESTful API endpoints
- Error handling and retry logic
- Loading states and optimistic updates
- Offline functionality support

### Performance Guidelines
- Code splitting and lazy loading
- Image optimization
- Bundle size optimization
- Caching strategies
- Virtual scrolling for large lists

### Security Considerations
- Input validation and sanitization
- XSS protection
- CSRF protection
- Secure authentication flow
- Data encryption for sensitive information

### Testing Strategy
- Unit tests for components
- Integration tests for workflows
- E2E tests for critical paths
- Accessibility testing
- Performance testing

### Naming Conventions
- PascalCase for components: `QuoteCard`, `CustomerList`
- camelCase for functions and variables: `createQuote`, `customerData`
- kebab-case for files: `quote-card.tsx`, `customer-list.tsx`
- SCREAMING_SNAKE_CASE for constants: `API_BASE_URL`, `MAX_FILE_SIZE`

### File Organization
- Group related components in folders
- Co-locate tests with components
- Separate business logic from UI components
- Use index files for clean imports

### AI Integration Guidelines
- Voice recognition for quote input
- Natural language processing for customer queries
- Predictive pricing algorithms
- Automated follow-up suggestions
- Smart categorization and tagging

### Localization
- Support for Dutch and French
- Date/time formatting per locale
- Currency formatting (EUR)
- Number formatting
- RTL support preparation

### Error Handling
- Graceful error boundaries
- User-friendly error messages
- Logging and monitoring
- Fallback UI states
- Network error recovery

### Data Management
- Optimistic updates for better UX
- Pagination for large datasets
- Search and filtering capabilities
- Data validation schemas
- Backup and sync strategies

## Development Workflow
1. Feature development in feature branches
2. Code review process
3. Automated testing pipeline
4. Staging environment testing
5. Production deployment

## Performance Targets
- First Contentful Paint < 1.5s
- Largest Contentful Paint < 2.5s
- Cumulative Layout Shift < 0.1
- First Input Delay < 100ms
- Bundle size < 500KB gzipped

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari 14+, Chrome Mobile 90+)

Remember: Always prioritize user experience, maintainable code, and performance when implementing features.