#!/bin/bash

# AI.qoute+crm Development Health Check Script
# Runs comprehensive checks before development

echo "🚀 AI.qoute+crm Development Health Check"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check Node.js version
echo ""
print_info "Checking Node.js version..."
node_version=$(node -v | cut -d'v' -f2)
required_version="18.0.0"
if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
    print_status 0 "Node.js version $node_version (>= $required_version required)"
else
    print_status 1 "Node.js version $node_version is too old (>= $required_version required)"
fi

# Check if node_modules exists
echo ""
print_info "Checking dependencies..."
if [ -d "node_modules" ]; then
    print_status 0 "Dependencies installed"
else
    print_warning "Dependencies not found. Running npm install..."
    npm install
    print_status $? "Dependencies installation"
fi

# Type checking
echo ""
print_info "Running TypeScript type check..."
npm run type-check > /dev/null 2>&1
print_status $? "TypeScript type check"

# Linting
echo ""
print_info "Running ESLint..."
npm run lint > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "ESLint check"
else
    print_warning "ESLint issues found. Running auto-fix..."
    npm run lint:fix > /dev/null 2>&1
    print_status $? "ESLint auto-fix"
fi

# Check for duplicate routes
echo ""
print_info "Checking for duplicate routes..."
npm run check:routes > /dev/null 2>&1
print_status $? "Route conflict check"

# Check if ports are available
echo ""
print_info "Checking port availability..."
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    print_warning "Port 3001 is already in use"
else
    print_status 0 "Port 3001 is available"
fi

# Summary
echo ""
echo "========================================"
print_info "Health check completed! Ready for development."
print_info "Run 'npm run dev' to start the development server."
echo ""
