{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "cSpell.words": ["Innovars", "offertes", "klanten", "projecten", "facturen", "instellingen", "analytics", "Vermeulen", "<PERSON><PERSON>", "<PERSON><PERSON>", "voor", "nieu<PERSON>", "betere", "oude", "manier", "gebruikers", "hele", "succesvol", "geladen", "fout", "voordelen", "automatische", "cust"], "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "emmet.includeLanguages": {"typescriptreact": "html"}, "cmake.sourceDirectory": "/Users/<USER>/AI.qoute + crm/node_modules/nan"}