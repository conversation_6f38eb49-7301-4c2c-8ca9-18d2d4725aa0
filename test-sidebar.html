<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glass-light {
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-dark {
            background: rgba(0, 0, 0, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .transition-all {
            transition: all 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 to-blue-900 min-h-screen text-white">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-40 bg-glass-light backdrop-blur-md border-r border-white/20 transition-all duration-300 ease-in-out w-64">
            <div class="flex flex-col h-full">
                <!-- Logo and Toggle -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-white/20">
                    <h1 id="logo" class="text-xl font-bold text-white">AI.qoute+crm</h1>
                    <button
                        id="toggleBtn"
                        class="p-1 rounded-md text-gray-300 hover:text-white hover:bg-glass-dark transition-colors"
                        title="Inklappen"
                    >
                        <svg id="chevron" class="h-4 w-4 transition-transform duration-200 ease-in-out" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                </div>

                <!-- Navigation -->
                <nav class="flex-1 px-4 py-6 space-y-2">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors bg-primary-600 text-white shadow-glow">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-300 hover:text-white hover:bg-glass-dark">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="nav-text">Offertes</span>
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-300 hover:text-white hover:bg-glass-dark">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span class="nav-text">Klanten</span>
                    </a>
                </nav>

                <!-- User info -->
                <div class="p-4 border-t border-white/20">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                            <span class="text-sm font-medium text-white">U</span>
                        </div>
                        <div class="ml-3 user-info">
                            <p class="text-sm font-medium text-white">Gebruiker</p>
                            <p class="text-xs text-gray-400">Premium Plan</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div id="mainContent" class="flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out ml-64">
            <header class="bg-glass-light backdrop-blur-sm border-b border-white/20 p-4">
                <h2 class="text-xl font-semibold">Dashboard</h2>
            </header>
            <main class="flex-1 overflow-y-auto bg-glass-light backdrop-blur-sm p-6">
                <div class="max-w-4xl mx-auto">
                    <h1 class="text-3xl font-bold mb-6">Welkom bij AI.qoute+crm</h1>
                    <p class="text-gray-300 mb-4">De sidebar is nu inschrijfbaar! Klik op het pijltje om de sidebar in/uit te klappen.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">Offertes</h3>
                            <p class="text-gray-300">Beheer uw offertes en facturen</p>
                        </div>
                        <div class="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">Klanten</h3>
                            <p class="text-gray-300">Beheer uw klantgegevens</p>
                        </div>
                        <div class="bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">Projecten</h3>
                            <p class="text-gray-300">Beheer uw projecten en taken</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let isCollapsed = false;
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const toggleBtn = document.getElementById('toggleBtn');
        const chevron = document.getElementById('chevron');
        const logo = document.getElementById('logo');
        const navTexts = document.querySelectorAll('.nav-text');
        const userInfo = document.querySelector('.user-info');

        toggleBtn.addEventListener('click', () => {
            isCollapsed = !isCollapsed;
            
            if (isCollapsed) {
                sidebar.classList.remove('w-64');
                sidebar.classList.add('w-16');
                mainContent.classList.remove('ml-64');
                mainContent.classList.add('ml-16');
                chevron.style.transform = 'rotate(180deg)';
                toggleBtn.title = 'Uitklappen';
                
                // Hide text elements
                logo.style.display = 'none';
                navTexts.forEach(text => text.style.display = 'none');
                userInfo.style.display = 'none';
                
                // Center icons
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('px-3');
                    link.classList.add('justify-center', 'px-2');
                });
            } else {
                sidebar.classList.remove('w-16');
                sidebar.classList.add('w-64');
                mainContent.classList.remove('ml-16');
                mainContent.classList.add('ml-64');
                chevron.style.transform = 'rotate(0deg)';
                toggleBtn.title = 'Inklappen';
                
                // Show text elements
                logo.style.display = 'block';
                navTexts.forEach(text => text.style.display = 'block');
                userInfo.style.display = 'block';
                
                // Restore original layout
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('justify-center', 'px-2');
                    link.classList.add('px-3');
                });
            }
        });
    </script>
</body>
</html> 