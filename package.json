{"name": "ai-quote-crm", "version": "1.0.0", "description": "AI-powered quote generation and customer management system for construction professionals", "type": "module", "scripts": {"dev": "NODE_OPTIONS='--trace-warnings' vite", "dev:brave": "NODE_OPTIONS='--trace-warnings' vite --open && open -a 'Brave Browser' http://localhost:5173", "dev:check": "./scripts/dev-check.sh && npm run dev", "build": "tsc && vite build", "build:analyze": "npm run build && open dist/stats.html", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "check:routes": "grep -R \"path=\\|Route path=\" src/ | grep -v node_modules | sort | uniq -d || echo 'No duplicate routes found'", "check:all": "npm run type-check && npm run lint && npm run format:check && npm run check:routes", "analyze": "npm run build && open dist/stats.html", "clean": "rm -rf dist node_modules/.vite", "reinstall": "rm -rf node_modules package-lock.json && npm install"}, "dependencies": {"@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@react-pdf/renderer": "^4.3.0", "autoprefixer": "^10.4.14", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.263.1", "postcss": "^8.4.24", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-pdf": "^7.5.1", "react-query": "^3.39.3", "react-router-dom": "^6.8.0", "react-speech-kit": "^2.0.1", "react-speech-recognition": "^3.10.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "zod": "^3.22.4", "zustand": "^4.4.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^6.0.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.4"}, "keywords": ["ai", "quote", "crm", "construction", "react", "typescript", "tailwindcss"], "author": "AI.qoute+crm Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ai-quote-crm.git"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}