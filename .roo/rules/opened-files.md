# Opened Files
## File Name
src/styles/globals.css
## File Content
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  /* Updated glass colors */
  --glass-light: rgba(255, 255, 255, 0.08);
  --glass-dark: rgba(17, 24, 39, 0.15);
  --glass-darker: rgba(0, 0, 0, 0.2);
  
  /* Updated primary colors */
  --primary-25: #f5f8ff;
  --primary-50: #eef4ff;
  --primary-100: #e0e9ff;
  --primary-200: #c7d8fe;
  --primary-300: #a5bffd;
  --primary-400: #7fa0fa;
  --primary-500: #6178f5;
  --primary-600: #4d5beb;
  --primary-700: #3e46d6;
  --primary-800: #333aad;
  --primary-900: #2f3589;
  --primary-950: #1e2252;
  
  /* Status colors */
  --success-500: #22c55e;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #0ea5e9;
}

/* Base Styles */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  /* Better scrollbar handling with fallbacks */
  -webkit-scrollbar-gutter: stable; /* WebKit first */
  scrollbar-gutter: stable; /* Standard property */
  font-size: 16px;
  line-height: 1.5;
  
  /* Fallback for browsers that don't support scrollbar-gutter */
  padding-right: 0.5rem;
  overflow-y: scroll;
}

/* Typography */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%);
  color: #f8fafc;
  min-height: 100vh;
  scrollbar-gutter: stable;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.25;
  margin-bottom: 0.75em;
  letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Paragraphs */
p {
  margin-bottom: 1.25em;
  line-height: 1.7;
  color: #e2e8f0;
}

/* Links */
a {
  color: var(--primary-400);
  text-decoration: none;
  transition: color 0.2s ease, opacity 0.2s ease;
}

a:hover {
  color: var(--primary-300);
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Buttons */
button, .btn {
  transition: all 0.2s ease;
  transform-origin: center;
}

button:active, .btn:active {
  transform: scale(0.98);
}

/* Form Elements */
input, textarea, select {
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  color: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-scale:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.5);
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Utility Classes */
.text-balance {
  /* Fallback for older browsers */
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  
  /* Modern browsers */
  text-wrap: balance;
}

.text-gradient {
  -webkit-background-clip: text; /* Safari/Chrome */
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* Fallback */
}

/* Custom Scrollbar - WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Standard scrollbar for Firefox */
@supports (scrollbar-width: thin) {
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) rgba(255, 255, 255, 0.05);
  }
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Glassmorphism Utilities */
.glass-light {
  background: var(--glass-light);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: var(--glass-dark);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Glasmorphism Components */
.glass-card {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg;
}

.glass-button {
  @apply bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/30 transition-all duration-300;
}

.glass-input {
  @apply bg-white/5 backdrop-blur-sm border border-white/20 focus:border-primary-400 rounded-lg;
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-transparent;
}

/* Button Hover Effects */
.btn-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-105 active:scale-95;
}

/* Card Hover Effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-glow hover:scale-[1.02];
}

/* Status Indicators */
.status-online {
  @apply w-2 h-2 bg-green-500 rounded-full animate-pulse;
}

.status-offline {
  @apply w-2 h-2 bg-red-500 rounded-full;
}

.status-busy {
  @apply w-2 h-2 bg-yellow-500 rounded-full animate-pulse;
}

/* Loading Spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
}

/* Custom Input Styles */
.input-glass {
  @apply bg-glass-dark backdrop-blur-sm border border-white/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

/* Custom Select Styles */
.select-glass {
  @apply bg-glass-dark backdrop-blur-sm border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

/* Notification Styles */
.notification {
  @apply p-4 rounded-lg border backdrop-blur-sm;
}

.notification-info {
  @apply bg-blue-500/20 border-blue-500/30 text-blue-300;
}

.notification-success {
  @apply bg-green-500/20 border-green-500/30 text-green-300;
}

.notification-warning {
  @apply bg-yellow-500/20 border-yellow-500/30 text-yellow-300;
}

.notification-error {
  @apply bg-red-500/20 border-red-500/30 text-red-300;
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 641px) {
  .desktop-hidden {
    display: none;
  }
}

/* Mobile Optimizations */
@media (max-width: 1024px) {
  .touch-manipulation {
    touch-action: manipulation;
  }

  .sidebar-mobile {
    overscroll-behavior: contain;
  }
}

/* Animation Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Touch Feedback */
.touch-feedback {
  -webkit-tap-highlight-color: transparent;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* Hardware Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

# Opened Files
## File Name
tailwind.config.js
## File Content
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // AI.qoute+crm brand colors
        primary: {
          25: '#f5f8ff',
          50: '#eef4ff',
          100: '#e0e9ff',
          200: '#c7d8fe',
          300: '#a5bffd',
          400: '#7fa0fa',
          500: '#6178f5', // Main brand color
          600: '#4d5beb',
          700: '#3e46d6',
          800: '#333aad',
          900: '#2f3589',
          950: '#1e2252',
        },
        // Glasmorphism colors
        glass: {
          light: 'rgba(255, 255, 255, 0.08)',
          dark: 'rgba(17, 24, 39, 0.15)',
          darker: 'rgba(0, 0, 0, 0.2)',
        },
        // Extended color palette
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e', // Base success
          600: '#16a34a', // Hover state
          700: '#15803d', // Active state
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b', // Base warning
          600: '#d97706', // Hover state
          700: '#b45309', // Active state
          800: '#92400e',
          900: '#78350f',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444', // Base error
          600: '#dc2626', // Hover state
          700: '#b91c1c', // Active state
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // New accent colors
        info: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
      },
      // Custom shadows
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.03)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px -1px rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.05)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -4px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.05)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.1)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
        'glass': '0 4px 30px rgba(0, 0, 0, 0.1)',
      },
      // Custom gradients
      backgroundImage: {
        'glass-gradient': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))',
        'primary-gradient': 'linear-gradient(135deg, var(--tw-gradient-stops))',
        'subtle-grid': 'linear-gradient(to right, #f8fafc 1px, transparent 1px), linear-gradient(to bottom, #f8fafc 1px, transparent 1px)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'glow': '0 0 20px rgba(59, 130, 246, 0.3)',
        'glow-lg': '0 0 40px rgba(59, 130, 246, 0.4)',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
} 
