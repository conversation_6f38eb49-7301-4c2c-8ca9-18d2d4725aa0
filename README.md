# AI.qoute+crm

Een AI-gestuurde offerteassistent en CRM systeem voor bouwprofessionals in België en Frankrijk.

## 🚀 Overzicht

AI.qoute+crm is een moderne web-applicatie die bouwprofessionals helpt bij het genereren van professionele offertes met behulp van AI-technologie. Het systeem integreert spraakherkenning, natuurlijke taalverwerking en geavanceerde CRM-functionaliteiten.

### Kernfunctionaliteiten

- 🤖 **AI Assistant (Rita)**: Intelligente offertegeneratie via spraak en tekst
- 📊 **Dashboard**: Real-time overzicht van KPI's en activiteiten
- 👥 **Klant Management**: Uitgebreide klantprofielen en communicatiegeschiedenis
- 📋 **Offerte Management**: Professionele offertes met templates en export
- 🏗️ **Project Management**: Project tracking en foto-documentatie
- 📱 **Mobile-First**: Responsive design voor alle apparaten
- 🌍 **Meertalig**: Ondersteuning voor Nederlands en Frans

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Glassmorphism design
- **UI Components**: Lucide React icons, Framer Motion
- **State Management**: Zustand, React Context
- **Forms**: React Hook Form, Zod validation
- **Routing**: React Router DOM
- **Notifications**: React Hot Toast
- **AI Integration**: OpenAI API, Speech Recognition

## 📁 Project Structuur

```
src/
├── components/          # Herbruikbare UI componenten
│   ├── ui/             # Basis UI componenten (Button, Input, etc.)
│   ├── dashboard/      # Dashboard-specifieke componenten
│   ├── quotes/         # Offerte management componenten
│   ├── customers/      # Klant management componenten
│   └── layout/         # Layout componenten (Sidebar, Header)
├── pages/              # Route componenten
├── hooks/              # Custom React hooks
├── utils/              # Utility functies
├── types/              # TypeScript type definities
├── constants/          # App-wide constanten
├── services/           # API services en externe integraties
├── contexts/           # React Context providers
├── api/                # API integratie laag
├── styles/             # Globale styles en CSS
└── assets/             # Statische assets (images, icons, fonts)
```

## 🚀 Snel Starten

### Vereisten

- Node.js 18+ 
- npm of yarn

### Installatie

1. **Clone de repository**
   ```bash
   git clone https://github.com/your-org/ai-quote-crm.git
   cd ai-quote-crm
   ```

2. **Installeer dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

### Development Scripts

```bash
npm run dev          # Start development server
npm run build        # Build voor productie
npm run preview      # Preview productie build
npm run lint         # ESLint check
npm run lint:fix     # ESLint auto-fix
npm run format       # Prettier formatting
npm run type-check   # TypeScript type checking
npm run test         # Run tests
npm run test:ui      # Run tests met UI
npm run check:routes # Controleer op dubbele routes
npm run analyze      # Bundle analyzer (opent stats.html)
```

### 🛠️ DevTools Power User Tips

#### Browser DevTools Shortcuts
- **⌘⌥I** (Mac) / **Ctrl+Shift+I** (Windows/Linux): Open DevTools
- **⌘⇧P** (Mac) / **Ctrl+Shift+P** (Windows/Linux): Command menu
- **⌘⇧C** (Mac) / **Ctrl+Shift+C** (Windows/Linux): Element selector
- **⌘⇧J** (Mac) / **Ctrl+Shift+J** (Windows/Linux): Console
- **⌘⇧M** (Mac) / **Ctrl+Shift+M** (Windows/Linux): Device toolbar

#### Network Tab Filters
Voeg deze filters toe via DevTools → Settings → Console filters:
```
-status-code:200 -status-code:304
```
Dit toont alleen failed requests voor snelle debugging.

#### Console Filters
- `-status-code:200` - Verberg succesvolle requests
- `-status-code:304` - Verberg cached responses  
- `error` - Toon alleen errors
- `warning` - Toon alleen warnings

#### Performance Tips
- **Performance tab**: Record user interactions voor performance analyse
- **Memory tab**: Heap snapshots voor memory leaks
- **Lighthouse**: Audit tab voor performance scores
- **Coverage tab**: Zie welke code wordt gebruikt

#### Debugging Features
- **Source maps**: Automatisch ingeschakeld voor development
- **Error boundaries**: React error boundaries voor graceful crashes
- **Toast notifications**: Automatische error feedback via `fetchWithToast`
- **Bundle analyzer**: `npm run analyze` voor bundle inspectie

## 🎨 Design System

### Kleuren
- **Primary**: Blue gradient (#3B82F6 naar #1E40AF)
- **Glass**: Transparante achtergronden met backdrop-blur
- **Status**: Groen (online), Rood (offline), Geel (busy)

### Typography
- **Font**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### Componenten
- **Button**: 7 varianten (default, destructive, outline, etc.)
- **Cards**: Glassmorphism effecten
- **Input**: Glass styling met focus states
- **Status Indicators**: Animated dots voor real-time status

## 🤖 AI Features

### Rita AI Assistant
- **Spraakherkenning**: Directe spraak naar tekst conversie
- **Natuurlijke Taalverwerking**: Begrijpt bouwsector terminologie
- **Prijsoptimalisatie**: Marktgebaseerde prijssuggesties
- **Suggesties**: Automatische werkensuggesties

### AI Capabilities
- Offerte generatie via spraak
- Prijsoptimalisatie op basis van marktdata
- Automatische categorisatie van werken
- Intelligente suggesties voor aanvullende diensten

## 📱 Mobile Features

- **Responsive Design**: Mobile-first approach
- **Touch-Friendly**: Geoptimaliseerd voor touch interfaces
- **PWA Ready**: Progressive Web App capabilities
- **Offline Support**: Basis functionaliteit offline beschikbaar

## 🌍 Internationalisatie

### Ondersteunde Talen
- **Nederlands** (primair)
- **Frans** (secundair)

### Locale Features
- Datum/tijd formatting
- Currency formatting (EUR)
- Number formatting
- RTL support voorbereiding

## 🔧 Configuratie

### Debugging & Development Tools

#### Source Maps & Error Tracking
- **Source maps**: Automatisch ingeschakeld voor development en productie
- **Trace warnings**: `NODE_OPTIONS='--trace-warnings'` voor betere error tracking
- **Console logging**: Alle fetch requests worden gelogd met status codes
- **Error boundaries**: React error boundaries voor graceful error handling

#### Bundle Analysis
```bash
npm run analyze  # Genereert en opent bundle analyzer
```
- Toont bundle grootte per module
- Gzip en Brotli compressie stats
- Tree map visualisatie van dependencies

#### Route Conflict Detection
```bash
npm run check:routes  # Controleer op dubbele routes
```
- Detecteert conflicterende route definities
- Helpt bij het voorkomen van routing bugs

#### Enhanced Fetch Error Handling
```typescript
import { fetchWithToast, get, post } from '@/lib/fetchWithToast'

// Automatische error handling met toasts
const response = await get('/api/quotes')
const newQuote = await post('/api/quotes', data, {
  toastMessage: 'Offerte succesvol aangemaakt!'
})
```

### Environment Variables
```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_AI_API_KEY=your_openai_api_key
VITE_SPEECH_API_KEY=your_speech_api_key
```

### API Endpoints
- `GET /api/quotes` - Haal offertes op
- `POST /api/quotes` - Maak nieuwe offerte
- `GET /api/customers` - Haal klanten op
- `POST /api/customers` - Voeg nieuwe klant toe
- `GET /api/ai/assistant` - AI assistant status
- `POST /api/ai/generate` - Genereer offerte met AI

## 🧪 Testing

```bash
npm run test         # Unit tests
npm run test:ui      # Visual testing
npm run test:coverage # Coverage report
```

### Test Types
- **Unit Tests**: Component testing met Vitest
- **Integration Tests**: API integration testing
- **E2E Tests**: End-to-end workflow testing
- **Accessibility Tests**: WCAG 2.1 AA compliance

## 📦 Deployment

### Build voor Productie
```bash
npm run build
```

### Deployment Platforms
- **Vercel**: Aanbevolen voor frontend
- **Netlify**: Alternatief voor static hosting
- **AWS S3**: Voor enterprise deployments

## 🤝 Bijdragen

1. Fork de repository
2. Maak een feature branch (`git checkout -b feature/amazing-feature`)
3. Commit je wijzigingen (`git commit -m 'Add amazing feature'`)
4. Push naar de branch (`git push origin feature/amazing-feature`)
5. Open een Pull Request

### Code Standards
- TypeScript voor alle nieuwe code
- ESLint + Prettier voor formatting
- Conventional Commits voor commit messages
- Unit tests voor nieuwe features

## 📄 Licentie

Dit project is gelicenseerd onder de MIT License - zie het [LICENSE](LICENSE) bestand voor details.

## 🆘 Support

- **Documentatie**: [docs.aiqoutecrm.com](https://docs.aiqoutecrm.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/ai-quote-crm/issues)
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Q1 2024
- [ ] WhatsApp integratie
- [ ] PDF export templates
- [ ] Advanced AI features

### Q2 2024
- [ ] Mobile app (React Native)
- [ ] Multi-tenant support
- [ ] Advanced analytics

### Q3 2024
- [ ] Enterprise features
- [ ] API marketplace
- [ ] Advanced integrations

---

**Gemaakt met ❤️ door het AI.qoute+crm Team** 

## 🚀 AI Quote Wizard

De AI Quote Wizard is een geavanceerde feature die automatisch offertes genereert op basis van project titel, afmetingen en foto's.

### ✨ Features

- **📷 Vision AI Analyse**: Automatische materiaal detectie uit foto's
- **📐 Slimme Calculaties**: Automatische kostenberekening op basis van afmetingen
- **✍️ AI Beschrijving**: Automatische generatie van professionele beschrijvingen
- **📄 PDF Export**: Directe PDF generatie van gegenereerde offertes
- **💾 Database Integratie**: Automatische opslag in het CRM systeem

### 🔧 Technische Implementatie

#### Frontend (React + TypeScript)
- **Route**: `/quotes/new-ai`
- **Component**: `AIQuoteWizard.tsx`
- **Features**:
  - Dynamische afmetingen formulier
  - Multi-photo upload met preview
  - Real-time validatie
  - Loading states en error handling
  - Side-by-side preview

#### API Flow
1. **POST** `/api/ai-quote`
2. **Input**: `{ title, dimensions[], photos[] }`
3. **Process**:
   - Photo analysis → material detection
   - Dimension calculations → cost breakdown
   - AI description generation
4. **Output**: `{ title, items[], subtotal, vat, total, description }`

#### Mock Implementation
- **Vision AI**: Random materiaal detectie (hout, tegels, aluminium, etc.)
- **Calculation Service**: Prijsregels per materiaal + arbeidskosten
- **GPT Integration**: Mock beschrijving generatie
- **PDF Generation**: @react-pdf/renderer voor professionele PDF's

### 🎯 User Flow

1. **Project Invoer**
   - Titel van het project
   - Afmetingen (lengte × breedte × hoogte × aantal)
   - Foto upload (meerdere foto's mogelijk)

2. **AI Analyse**
   - Vision model detecteert materialen
   - Afmetingen worden gecombineerd tot m²
   - Calculatieservice berekent kosten
   - GPT genereert beschrijving

3. **Resultaat**
   - Volledige offerte met items en prijzen
   - Professionele beschrijving
   - Directe opslag in database
   - PDF export mogelijkheid

### 📊 Prijsregels

| Materiaal | Materiaal Prijs/m² | Arbeid/m² | Totaal/m² |
|-----------|-------------------|-----------|-----------|
| Hout      | €45              | €25       | €70       |
| Tegels    | €65              | €35       | €100      |
| Aluminium | €85              | €40       | €125      |
| Glas      | €120             | €50       | €170      |
| Staal     | €95              | €45       | €140      |
| Kunststof | €55              | €30       | €85       |
| Beton     | €35              | €20       | €55       |
| Marmer    | €150             | €60       | €210      |

### 🔄 API Endpoints

#### POST /api/ai-quote
```typescript
interface AIQuoteRequest {
  title: string;
  dimensions: {
    id: string;
    length: number;
    width: number;
    height: number;
    quantity: number;
    description: string;
  }[];
  photos: string[]; // base64 encoded
}

interface AIQuoteResponse {
  title: string;
  items: QuoteItem[];
  subtotal: number;
  vat: number;
  total: number;
  description: string;
}
```

### 🛠️ Development

#### Lokale Testen
1. Start de development server: `npm run dev`
2. Navigeer naar `/quotes/new-ai`
3. Vul project gegevens in
4. Upload test foto's
5. Klik "AI Quote Genereren"
6. Bekijk het resultaat in de preview

#### Mock Data
- Test foto's worden automatisch geanalyseerd
- Random materiaal detectie voor demo
- Realistische prijsberekeningen
- Professionele beschrijvingen

### 📁 Bestanden

```
src/
├── pages/
│   └── AIQuoteWizard.tsx          # Hoofdcomponent
├── components/
│   └── quotes/
│       └── PDFGenerator.tsx       # PDF export
├── api/
│   ├── ai-quote.ts               # AI Quote API
│   └── quotes.ts                 # Quote management
└── public/
    └── api/
        └── ai-quote.js           # Mock API endpoint
```

### 🚀 Toekomstige Uitbreidingen

- **Echte Vision AI**: Hugging Face DINOv2 + SAM
- **FastAPI Backend**: Python microservices
- **LangChain Integration**: Qwen2.5-Coder-32B
- **Database Schema**: Prisma + PostgreSQL
- **Real-time Updates**: WebSocket integratie

### 📝 Voorbeeld Gebruik

```typescript
// Genereer AI Quote
const response = await fetch('/api/ai-quote', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'Badkamer Renovatie',
    dimensions: [
      {
        length: 3.5,
        width: 2.8,
        height: 2.4,
        quantity: 1,
        description: 'Badkamer tegels'
      }
    ],
    photos: ['base64_encoded_image_1', 'base64_encoded_image_2']
  })
});

const quote = await response.json();
// Resultaat: Volledige offerte met items, prijzen en beschrijving
```

---

**AI Quote Wizard** maakt het mogelijk om binnen minuten professionele offertes te genereren op basis van eenvoudige project gegevens en foto's. De AI analyseert automatisch de materialen, berekent de kosten en genereert een complete offerte die direct kan worden opgeslagen en gedeeld. 